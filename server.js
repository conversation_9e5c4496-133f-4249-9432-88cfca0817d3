const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const newsRoutes = require('./routes/news');
const { initDatabase } = require('./config/database');
const { startScheduler } = require('./utils/scheduler');

const app = express();
const PORT = process.env.PORT || 5020;

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً'
  }
});
app.use('/api/', limiter);

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/news', newsRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Iraqi News API is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في API الأخبار العراقية',
    version: '1.0.0',
    endpoints: {
      'GET /api/news': 'جلب جميع الأخبار',
      'GET /api/news/latest': 'أحدث الأخبار',
      'GET /api/news/source/:source': 'أخبار من مصدر محدد',
      'GET /api/news/category/:category': 'أخبار حسب الفئة',
      'GET /api/news/search/:query': 'البحث في الأخبار',
      'GET /health': 'فحص حالة الخادم'
    },
    sources: [
      'shafaq',
      'hathalyoum',
      'media964',
      'alsumaria'
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'حدث خطأ في الخادم',
    message: process.env.NODE_ENV === 'development' ? err.message : 'خطأ داخلي'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'الصفحة غير موجودة',
    message: 'المسار المطلوب غير متاح'
  });
});

// Initialize database and start server
async function startServer() {
  try {
    await initDatabase();
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    
    // Start the scheduler for news updates
    startScheduler();
    console.log('✅ تم تشغيل جدولة تحديث الأخبار');
    
    app.listen(PORT, () => {
      console.log(`🚀 خادم API الأخبار العراقية يعمل على المنفذ ${PORT}`);
      console.log(`🌐 الرابط: http://localhost:${PORT}`);
      console.log(`📊 لوحة التحكم: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    console.error('❌ خطأ في تشغيل الخادم:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 إيقاف الخادم...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 إيقاف الخادم...');
  process.exit(0);
});

startServer();
