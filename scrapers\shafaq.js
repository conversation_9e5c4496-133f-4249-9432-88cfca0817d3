const axios = require('axios');
const cheerio = require('cheerio');
const moment = require('moment-timezone');
const News = require('../models/News');
const { makeRequest, cleanArabicText, detectCategoryFromText, globalRateLimiter } = require('../utils/scraper-helpers');

class ShafaqScraper {
  constructor() {
    this.baseUrl = 'https://shafaq.com';
    this.source = 'shafaq';
    this.timezone = 'Asia/Baghdad';
  }

  // Main scraping function
  async scrapeNews() {
    try {
      console.log('🔄 بدء جمع الأخبار من شفق نيوز...');
      
      const articles = await this.getArticlesList();
      let savedCount = 0;
      let errorCount = 0;

      for (const article of articles) {
        try {
          const fullArticle = await this.getArticleDetails(article);
          if (fullArticle) {
            const news = new News(fullArticle);
            await news.save();
            savedCount++;
            console.log(`✅ تم حفظ: ${fullArticle.title.substring(0, 50)}...`);
          }
        } catch (error) {
          errorCount++;
          console.error(`❌ خطأ في حفظ المقال: ${error.message}`);
        }
      }

      console.log(`✅ شفق نيوز: تم حفظ ${savedCount} خبر، ${errorCount} خطأ`);
      return { saved: savedCount, errors: errorCount };
    } catch (error) {
      console.error('❌ خطأ في جمع أخبار شفق نيوز:', error.message);
      throw error;
    }
  }

  // Get list of articles from main page
  async getArticlesList() {
    try {
      await globalRateLimiter.waitIfNeeded();
      const response = await makeRequest(`${this.baseUrl}/ar`);

      const $ = cheerio.load(response.data);
      const articles = [];

      // Extract from main news links based on actual website structure
      $('a[href*="/ar/"]').each((index, element) => {
        const $element = $(element);
        const href = $element.attr('href');

        // Skip non-article links
        if (!href || href.includes('#') || href.includes('javascript') ||
            href.includes('/tags/') || href.includes('/contact') ||
            href.includes('/rss') || href === '/ar' || href === '/ar/' ||
            href.includes('facebook') || href.includes('twitter') ||
            href.includes('youtube') || href.includes('instagram')) {
          return;
        }

        const title = $element.text().trim();

        if (title && title.length > 15 && title.length < 200) {
          const fullUrl = href.startsWith('http') ? href : `${this.baseUrl}${href}`;

          // Avoid duplicates
          if (!articles.some(article => article.url === fullUrl)) {
            articles.push({
              title: title,
              url: fullUrl,
              summary: null
            });
          }
        }
      });

      // Filter out invalid articles and clean up
      const validArticles = articles.filter(article => {
        return article.title &&
               article.url &&
               article.title.length > 10 &&
               !article.title.includes('شفق نيوز') &&
               !article.title.includes('عرض الكل') &&
               !article.title.includes('المزيد') &&
               !article.title.includes('اتصل بنا') &&
               !article.title.includes('RSS') &&
               !article.url.includes('/tags/') &&
               !article.url.includes('/contact');
      });

      console.log(`📰 شفق نيوز: تم العثور على ${validArticles.length} مقال صالح من أصل ${articles.length}`);
      return validArticles.slice(0, 15); // Limit to 15 articles per scrape
    } catch (error) {
      console.error('خطأ في جلب قائمة المقالات من شفق نيوز:', error.message);
      return [];
    }
  }

  // Get full article details
  async getArticleDetails(article) {
    try {
      await globalRateLimiter.waitIfNeeded();
      const response = await makeRequest(article.url);

      const $ = cheerio.load(response.data);
      
      // Extract content
      const content = this.extractContent($);
      const publishedDate = this.extractPublishedDate($);
      const author = this.extractAuthor($);
      const category = this.extractCategory($);
      const imageUrl = this.extractImageUrl($);
      const tags = this.extractTags($);

      return {
        title: cleanArabicText(article.title),
        content: cleanArabicText(content),
        summary: cleanArabicText(article.summary) || cleanArabicText(content).substring(0, 200) + '...',
        url: article.url,
        source: this.source,
        category: category || detectCategoryFromText(article.title + ' ' + content),
        author: author,
        published_date: publishedDate,
        image_url: imageUrl,
        tags: tags
      };
    } catch (error) {
      console.error(`خطأ في جلب تفاصيل المقال ${article.url}:`, error.message);
      return null;
    }
  }

  // Extract article content
  extractContent($) {
    const contentSelectors = [
      '.article-content',
      '.post-content',
      '.entry-content',
      '.content',
      '.story-content',
      '.news-content',
      'article .content',
      '.main-content p'
    ];

    for (const selector of contentSelectors) {
      const content = $(selector).text().trim();
      if (content && content.length > 100) {
        return content;
      }
    }

    // Fallback: get all paragraphs
    const paragraphs = $('p').map((i, el) => $(el).text().trim()).get();
    return paragraphs.filter(p => p.length > 20).join('\n\n');
  }

  // Extract published date
  extractPublishedDate($) {
    const dateSelectors = [
      '.date',
      '.publish-date',
      '.post-date',
      '.article-date',
      'time',
      '.timestamp',
      '[datetime]'
    ];

    for (const selector of dateSelectors) {
      const dateElement = $(selector).first();
      if (dateElement.length) {
        const dateText = dateElement.attr('datetime') || dateElement.text().trim();
        const parsedDate = moment.tz(dateText, this.timezone);
        if (parsedDate.isValid()) {
          return parsedDate.format('YYYY-MM-DD HH:mm:ss');
        }
      }
    }

    // Default to current time if no date found
    return moment.tz(this.timezone).format('YYYY-MM-DD HH:mm:ss');
  }

  // Extract author
  extractAuthor($) {
    const authorSelectors = [
      '.author',
      '.by-author',
      '.post-author',
      '.article-author',
      '.writer'
    ];

    for (const selector of authorSelectors) {
      const author = $(selector).text().trim();
      if (author) {
        return author.replace(/^(بقلم|كتب|المحرر|الكاتب):?\s*/i, '');
      }
    }

    return 'شفق نيوز';
  }

  // Extract category
  extractCategory($) {
    const categorySelectors = [
      '.category',
      '.post-category',
      '.article-category',
      '.section',
      '.breadcrumb a:last-child'
    ];

    for (const selector of categorySelectors) {
      const category = $(selector).text().trim();
      if (category) {
        return this.mapCategory(category);
      }
    }

    return 'general';
  }

  // Extract image URL
  extractImageUrl($) {
    const imageSelectors = [
      '.article-image img',
      '.post-image img',
      '.featured-image img',
      '.content img:first',
      'article img:first'
    ];

    for (const selector of imageSelectors) {
      const img = $(selector).first();
      if (img.length) {
        const src = img.attr('src') || img.attr('data-src');
        if (src) {
          return src.startsWith('http') ? src : `${this.baseUrl}${src}`;
        }
      }
    }

    return null;
  }

  // Extract tags
  extractTags($) {
    const tags = [];
    
    $('.tag, .tags a, .post-tags a').each((i, el) => {
      const tag = $(el).text().trim();
      if (tag) tags.push(tag);
    });

    return tags.length > 0 ? tags.join(',') : null;
  }

  // Map Arabic categories to English
  mapCategory(arabicCategory) {
    const categoryMap = {
      'سياسة': 'politics',
      'اقتصاد': 'economy',
      'أمن': 'security',
      'رياضة': 'sports',
      'ثقافة': 'culture',
      'تكنولوجيا': 'technology',
      'صحة': 'health',
      'محلي': 'general',
      'عربي': 'general',
      'دولي': 'general'
    };

    for (const [arabic, english] of Object.entries(categoryMap)) {
      if (arabicCategory.includes(arabic)) {
        return english;
      }
    }

    return 'general';
  }
}

module.exports = ShafaqScraper;
