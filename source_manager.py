#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة وفحص مصادر الأخبار العراقية
"""

import requests
import feedparser
from bs4 import BeautifulSoup
import logging
from datetime import datetime
import pytz
from typing import List, Dict, Tuple
import json
import time

logger = logging.getLogger(__name__)

class SourceManager:
    def __init__(self, database):
        """تهيئة مدير المصادر"""
        self.db = database
        self.iraq_tz = pytz.timezone('Asia/Baghdad')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # تهيئة المصادر الافتراضية إذا لم تكن موجودة
        self.init_default_sources()
    
    def init_default_sources(self):
        """إضافة المصادر الافتراضية إذا لم تكن موجودة"""
        try:
            existing_sources = self.db.get_all_sources()
            if len(existing_sources) == 0:
                logger.info("🔄 إضافة المصادر الافتراضية...")
                
                default_sources = [
                    {
                        'name': 'الفرات نيوز',
                        'url': 'https://alforatnews.iq/news/iraq',
                        'type': 'website',
                        'is_active': True,
                        'selectors': json.dumps({
                            'container': 'article, .news-item, .post',
                            'title': 'h1, h2, h3, .title',
                            'content': 'p, .content, .summary',
                            'link': 'a'
                        }),
                        'description': 'موقع الفرات نيوز للأخبار العراقية'
                    },
                    {
                        'name': 'شفق نيوز',
                        'url': 'https://shafaq.com/ar',
                        'type': 'website',
                        'is_active': True,
                        'selectors': json.dumps({
                            'container': 'article, .news-item, .post',
                            'title': 'h1, h2, h3, .title',
                            'content': 'p, .content, .summary',
                            'link': 'a'
                        }),
                        'description': 'موقع شفق نيوز للأخبار العراقية'
                    },
                    {
                        'name': 'شفق نيوز RSS',
                        'url': 'https://shafaq.com/rss',
                        'type': 'rss',
                        'is_active': True,
                        'selectors': '',
                        'description': 'تغذية RSS لموقع شفق نيوز'
                    },
                    {
                        'name': 'السومرية نيوز',
                        'url': 'https://www.alsumaria.tv/iraq-news/48/%D9%85%D8%AD%D9%84%D9%8A%D8%A7%D8%AA',
                        'type': 'website',
                        'is_active': True,
                        'selectors': json.dumps({
                            'container': 'article, .news-item, .post',
                            'title': 'h1, h2, h3, .title',
                            'content': 'p, .content, .summary',
                            'link': 'a'
                        }),
                        'description': 'موقع السومرية نيوز للأخبار العراقية'
                    },
                    {
                        'name': 'الجبال نيوز',
                        'url': 'https://aljeebal.com/',
                        'type': 'website',
                        'is_active': True,
                        'selectors': json.dumps({
                            'container': 'article, .news-item, .post',
                            'title': 'h1, h2, h3, .title',
                            'content': 'p, .content, .summary',
                            'link': 'a'
                        }),
                        'description': 'موقع الجبال نيوز للأخبار العراقية'
                    },
                    {
                        'name': 'بغداد اليوم',
                        'url': 'https://baghdadtoday.news/lastnews',
                        'type': 'website',
                        'is_active': True,
                        'selectors': json.dumps({
                            'container': 'article, .news-item, .post',
                            'title': 'h1, h2, h3, .title',
                            'content': 'p, .content, .summary',
                            'link': 'a'
                        }),
                        'description': 'موقع بغداد اليوم للأخبار العراقية'
                    }
                ]
                
                for source in default_sources:
                    self.db.add_source(source)
                
                logger.info(f"✅ تم إضافة {len(default_sources)} مصدر افتراضي")
                
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المصادر الافتراضية: {str(e)}")
    
    def check_source_connectivity(self, source: Dict) -> Tuple[bool, str]:
        """فحص إمكانية الوصول للمصدر"""
        try:
            logger.info(f"🔍 فحص المصدر: {source['name']}")
            
            response = self.session.get(source['url'], timeout=15)
            response.raise_for_status()
            
            # فحص نوع المحتوى
            content_type = response.headers.get('content-type', '').lower()
            
            if source['type'] == 'rss':
                if 'xml' in content_type or 'rss' in content_type:
                    # فحص صحة RSS
                    feed = feedparser.parse(response.content)
                    if feed.bozo == 0 and len(feed.entries) > 0:
                        return True, f"RSS صحيح - {len(feed.entries)} عنصر"
                    else:
                        return False, "RSS غير صحيح أو فارغ"
                else:
                    return False, "المحتوى ليس RSS"
            
            elif source['type'] == 'website':
                if 'html' in content_type:
                    # فحص وجود محتوى HTML
                    soup = BeautifulSoup(response.content, 'html.parser')
                    if soup.find('title'):
                        return True, f"موقع ويب صحيح - {len(response.content)} بايت"
                    else:
                        return False, "HTML غير صحيح"
                else:
                    return False, "المحتوى ليس HTML"
            
            return True, "تم الوصول بنجاح"
            
        except requests.exceptions.Timeout:
            return False, "انتهت مهلة الاتصال"
        except requests.exceptions.ConnectionError:
            return False, "خطأ في الاتصال"
        except requests.exceptions.HTTPError as e:
            return False, f"خطأ HTTP: {e.response.status_code}"
        except Exception as e:
            return False, f"خطأ: {str(e)}"
    
    def test_source_scraping(self, source: Dict) -> Tuple[bool, str, int]:
        """اختبار جمع الأخبار من المصدر"""
        try:
            logger.info(f"🧪 اختبار جمع الأخبار من: {source['name']}")
            
            if source['type'] == 'rss':
                return self._test_rss_scraping(source)
            elif source['type'] == 'website':
                return self._test_website_scraping(source)
            else:
                return False, "نوع مصدر غير مدعوم", 0
                
        except Exception as e:
            return False, f"خطأ في الاختبار: {str(e)}", 0
    
    def _test_rss_scraping(self, source: Dict) -> Tuple[bool, str, int]:
        """اختبار جمع الأخبار من RSS"""
        try:
            feed = feedparser.parse(source['url'])
            
            if feed.bozo != 0:
                return False, "RSS غير صحيح", 0
            
            entries_count = len(feed.entries)
            if entries_count == 0:
                return False, "لا توجد عناصر في RSS", 0
            
            # فحص العناصر الأولى
            valid_entries = 0
            for entry in feed.entries[:5]:
                if entry.get('title') and entry.get('link'):
                    valid_entries += 1
            
            if valid_entries > 0:
                return True, f"تم العثور على {valid_entries} عنصر صحيح من أصل {entries_count}", entries_count
            else:
                return False, "لا توجد عناصر صحيحة", entries_count
                
        except Exception as e:
            return False, f"خطأ في RSS: {str(e)}", 0
    
    def _test_website_scraping(self, source: Dict) -> Tuple[bool, str, int]:
        """اختبار جمع الأخبار من موقع ويب"""
        try:
            response = self.session.get(source['url'], timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # استخدام selectors إذا كانت متوفرة
            selectors = {}
            if source.get('selectors'):
                try:
                    selectors = json.loads(source['selectors'])
                except:
                    pass
            
            # selectors افتراضية
            container_selector = selectors.get('container', 'article, .news-item, .post, .entry')
            title_selector = selectors.get('title', 'h1, h2, h3, .title, .headline')
            
            # البحث عن عناصر الأخبار
            news_elements = soup.select(container_selector)
            
            if not news_elements:
                return False, "لم يتم العثور على عناصر أخبار", 0
            
            valid_news = 0
            for element in news_elements[:10]:  # فحص أول 10 عناصر
                title_elem = element.select_one(title_selector)
                if title_elem and title_elem.get_text().strip():
                    valid_news += 1
            
            if valid_news > 0:
                return True, f"تم العثور على {valid_news} خبر صحيح من أصل {len(news_elements)}", len(news_elements)
            else:
                return False, "لا توجد أخبار صحيحة", len(news_elements)
                
        except Exception as e:
            return False, f"خطأ في الموقع: {str(e)}", 0
    
    def check_all_sources(self) -> Dict:
        """فحص جميع المصادر"""
        try:
            sources = self.db.get_all_sources()
            results = {
                'total_checked': 0,
                'successful': 0,
                'failed': 0,
                'details': []
            }
            
            for source in sources:
                results['total_checked'] += 1
                
                # فحص الاتصال
                is_connected, conn_msg = self.check_source_connectivity(source)
                
                # فحص جمع الأخبار
                can_scrape, scrape_msg, news_count = False, "لم يتم الفحص", 0
                if is_connected:
                    can_scrape, scrape_msg, news_count = self.test_source_scraping(source)
                
                # تحديث حالة المصدر في قاعدة البيانات
                if is_connected and can_scrape:
                    self.db.update_source_status(source['id'], 'success')
                    results['successful'] += 1
                    status = 'نجح'
                else:
                    error_msg = conn_msg if not is_connected else scrape_msg
                    self.db.update_source_status(source['id'], 'error', error_msg)
                    results['failed'] += 1
                    status = 'فشل'
                
                results['details'].append({
                    'id': source['id'],
                    'name': source['name'],
                    'url': source['url'],
                    'type': source['type'],
                    'is_active': source['is_active'],
                    'status': status,
                    'connection': is_connected,
                    'connection_msg': conn_msg,
                    'scraping': can_scrape,
                    'scraping_msg': scrape_msg,
                    'news_count': news_count
                })
                
                time.sleep(1)  # توقف قصير بين الفحوصات
            
            logger.info(f"🎉 انتهى فحص المصادر: {results['successful']} نجح، {results['failed']} فشل")
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص المصادر: {str(e)}")
            return {
                'total_checked': 0,
                'successful': 0,
                'failed': 0,
                'details': [],
                'error': str(e)
            }
