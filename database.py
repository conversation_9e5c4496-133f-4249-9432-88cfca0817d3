#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات لنظام أخبار العراق
"""

import sqlite3
import logging
from datetime import datetime
import pytz
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class Database:
    def __init__(self, db_path: str = 'iraqi_news.db'):
        """تهيئة قاعدة البيانات"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول الأخبار
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS news (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        content TEXT,
                        summary TEXT,
                        url TEXT UNIQUE,
                        source TEXT NOT NULL,
                        category TEXT DEFAULT 'عام',
                        published_date DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        is_iraqi BOOLEAN DEFAULT 1,
                        keywords TEXT
                    )
                ''')

                # جدول مصادر الأخبار
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS news_sources (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        url TEXT NOT NULL,
                        type TEXT NOT NULL DEFAULT 'website',
                        is_active BOOLEAN DEFAULT 1,
                        last_check DATETIME,
                        last_success DATETIME,
                        status TEXT DEFAULT 'unknown',
                        error_count INTEGER DEFAULT 0,
                        success_count INTEGER DEFAULT 0,
                        total_news_collected INTEGER DEFAULT 0,
                        selectors TEXT,
                        description TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # فهرسة للبحث السريع
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_published_date ON news(published_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_source ON news(source)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_category ON news(category)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_is_iraqi ON news(is_iraqi)')

                # فهرسة لجدول المصادر
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sources_active ON news_sources(is_active)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sources_type ON news_sources(type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sources_status ON news_sources(status)')
                
                conn.commit()
                logger.info("✅ تم تهيئة قاعدة البيانات بنجاح")
                
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة قاعدة البيانات: {str(e)}")
            raise
    
    def save_news(self, news_data: Dict) -> bool:
        """حفظ خبر جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR IGNORE INTO news 
                    (title, content, summary, url, source, category, published_date, is_iraqi, keywords)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    news_data.get('title', ''),
                    news_data.get('content', ''),
                    news_data.get('summary', ''),
                    news_data.get('url', ''),
                    news_data.get('source', ''),
                    news_data.get('category', 'عام'),
                    news_data.get('published_date'),
                    news_data.get('is_iraqi', True),
                    news_data.get('keywords', '')
                ))
                
                if cursor.rowcount > 0:
                    logger.info(f"✅ تم حفظ: {news_data.get('title', '')[:50]}...")
                    return True
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الخبر: {str(e)}")
            return False
    
    def get_today_news(self, limit: int = 50) -> List[Dict]:
        """جلب أخبار اليوم"""
        try:
            iraq_tz = pytz.timezone('Asia/Baghdad')
            today = datetime.now(iraq_tz).strftime('%Y-%m-%d')
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM news 
                    WHERE DATE(published_date) = ? AND is_iraqi = 1
                    ORDER BY published_date DESC 
                    LIMIT ?
                ''', (today, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب أخبار اليوم: {str(e)}")
            return []
    
    def get_all_news(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """جلب جميع الأخبار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM news 
                    WHERE is_iraqi = 1
                    ORDER BY published_date DESC 
                    LIMIT ? OFFSET ?
                ''', (limit, offset))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الأخبار: {str(e)}")
            return []
    
    def search_news(self, query: str, limit: int = 50) -> List[Dict]:
        """البحث في الأخبار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM news 
                    WHERE (title LIKE ? OR content LIKE ? OR keywords LIKE ?) 
                    AND is_iraqi = 1
                    ORDER BY published_date DESC 
                    LIMIT ?
                ''', (f'%{query}%', f'%{query}%', f'%{query}%', limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"❌ خطأ في البحث: {str(e)}")
            return []
    
    def get_news_by_category(self, category: str, limit: int = 50) -> List[Dict]:
        """جلب الأخبار حسب الفئة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM news 
                    WHERE category = ? AND is_iraqi = 1
                    ORDER BY published_date DESC 
                    LIMIT ?
                ''', (category, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب أخبار الفئة: {str(e)}")
            return []
    
    def get_stats(self) -> Dict:
        """جلب إحصائيات الأخبار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # إجمالي الأخبار
                cursor.execute('SELECT COUNT(*) FROM news WHERE is_iraqi = 1')
                total_news = cursor.fetchone()[0]
                
                # أخبار اليوم
                iraq_tz = pytz.timezone('Asia/Baghdad')
                today = datetime.now(iraq_tz).strftime('%Y-%m-%d')
                cursor.execute('SELECT COUNT(*) FROM news WHERE DATE(published_date) = ? AND is_iraqi = 1', (today,))
                today_news = cursor.fetchone()[0]
                
                # إحصائيات المصادر
                cursor.execute('''
                    SELECT source, COUNT(*) as count 
                    FROM news WHERE is_iraqi = 1 
                    GROUP BY source 
                    ORDER BY count DESC
                ''')
                sources = [{'source': row[0], 'count': row[1]} for row in cursor.fetchall()]
                
                # إحصائيات الفئات
                cursor.execute('''
                    SELECT category, COUNT(*) as count 
                    FROM news WHERE is_iraqi = 1 
                    GROUP BY category 
                    ORDER BY count DESC
                ''')
                categories = [{'category': row[0], 'count': row[1]} for row in cursor.fetchall()]
                
                return {
                    'total_news': total_news,
                    'today_news': today_news,
                    'sources': sources,
                    'categories': categories
                }
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الإحصائيات: {str(e)}")
            return {'total_news': 0, 'today_news': 0, 'sources': [], 'categories': []}
    
    def clear_all_news(self) -> int:
        """حذف جميع الأخبار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('SELECT COUNT(*) FROM news')
                count = cursor.fetchone()[0]

                cursor.execute('DELETE FROM news')
                cursor.execute('DELETE FROM sqlite_sequence WHERE name="news"')

                conn.commit()
                logger.info(f"🗑️ تم حذف {count} خبر من قاعدة البيانات")
                return count

        except Exception as e:
            logger.error(f"❌ خطأ في حذف الأخبار: {str(e)}")
            return 0

    def delete_news_by_id(self, news_id: int) -> bool:
        """حذف خبر بالمعرف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('DELETE FROM news WHERE id = ?', (news_id,))

                if cursor.rowcount > 0:
                    return True
                else:
                    return False

        except Exception as e:
            logger.error(f"❌ خطأ في حذف الخبر {news_id}: {str(e)}")
            return False

    def delete_news_by_keywords(self, keywords: List[str]) -> int:
        """حذف الأخبار التي تحتوي على كلمات معينة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # بناء شرط البحث
                conditions = []
                params = []

                for keyword in keywords:
                    conditions.append("(title LIKE ? OR content LIKE ?)")
                    params.extend([f'%{keyword}%', f'%{keyword}%'])

                if not conditions:
                    return 0

                where_clause = " OR ".join(conditions)

                # عد الأخبار أولاً
                cursor.execute(f'SELECT COUNT(*) FROM news WHERE {where_clause}', params)
                count = cursor.fetchone()[0]

                # حذف الأخبار
                cursor.execute(f'DELETE FROM news WHERE {where_clause}', params)

                conn.commit()
                logger.info(f"🗑️ تم حذف {count} خبر يحتوي على الكلمات المحددة")
                return count

        except Exception as e:
            logger.error(f"❌ خطأ في حذف الأخبار بالكلمات: {str(e)}")
            return 0

    # ==================== وظائف إدارة المصادر ====================

    def add_source(self, source_data: Dict) -> bool:
        """إضافة مصدر جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO news_sources
                    (name, url, type, is_active, selectors, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    source_data.get('name', ''),
                    source_data.get('url', ''),
                    source_data.get('type', 'website'),
                    source_data.get('is_active', True),
                    source_data.get('selectors', ''),
                    source_data.get('description', '')
                ))

                logger.info(f"✅ تم إضافة المصدر: {source_data.get('name', '')}")
                return True

        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المصدر: {str(e)}")
            return False

    def get_all_sources(self) -> List[Dict]:
        """جلب جميع المصادر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM news_sources
                    ORDER BY name
                ''')

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"❌ خطأ في جلب المصادر: {str(e)}")
            return []

    def get_active_sources(self) -> List[Dict]:
        """جلب المصادر النشطة فقط"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM news_sources
                    WHERE is_active = 1
                    ORDER BY name
                ''')

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"❌ خطأ في جلب المصادر النشطة: {str(e)}")
            return []

    def get_source_by_id(self, source_id: int) -> Optional[Dict]:
        """جلب مصدر بالمعرف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM news_sources
                    WHERE id = ?
                ''', (source_id,))

                row = cursor.fetchone()
                return dict(row) if row else None

        except Exception as e:
            logger.error(f"❌ خطأ في جلب المصدر: {str(e)}")
            return None

    def get_sources_by_name(self, name: str) -> List[Dict]:
        """جلب المصادر بالاسم"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM news_sources
                    WHERE name = ?
                ''', (name,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"❌ خطأ في جلب المصادر بالاسم: {str(e)}")
            return []

    def update_source(self, source_id: int, source_data: Dict) -> bool:
        """تحديث مصدر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # بناء استعلام التحديث ديناميكياً
                update_fields = []
                update_values = []

                for field in ['url', 'type', 'is_active', 'selectors', 'description']:
                    if field in source_data:
                        update_fields.append(f"{field} = ?")
                        update_values.append(source_data[field])

                # إضافة name فقط إذا كان مختلفاً
                if 'name' in source_data:
                    # فحص إذا كان الاسم مختلف عن الحالي
                    cursor.execute('SELECT name FROM news_sources WHERE id = ?', (source_id,))
                    current_name = cursor.fetchone()
                    if current_name and current_name[0] != source_data['name']:
                        update_fields.append("name = ?")
                        update_values.append(source_data['name'])

                if not update_fields:
                    return True  # لا توجد تحديثات

                # إضافة updated_at
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                update_values.append(source_id)

                query = f"UPDATE news_sources SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(query, update_values)

                if cursor.rowcount > 0:
                    logger.info(f"✅ تم تحديث المصدر ID: {source_id}")
                    return True
                else:
                    return False

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث المصدر: {str(e)}")
            return False

    def delete_source(self, source_id: int) -> bool:
        """حذف مصدر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # جلب اسم المصدر أولاً
                cursor.execute('SELECT name FROM news_sources WHERE id = ?', (source_id,))
                row = cursor.fetchone()
                source_name = row[0] if row else 'غير معروف'

                # حذف المصدر
                cursor.execute('DELETE FROM news_sources WHERE id = ?', (source_id,))

                if cursor.rowcount > 0:
                    logger.info(f"🗑️ تم حذف المصدر: {source_name}")
                    return True
                else:
                    return False

        except Exception as e:
            logger.error(f"❌ خطأ في حذف المصدر: {str(e)}")
            return False

    def update_source_status(self, source_id: int, status: str, error_msg: str = None) -> bool:
        """تحديث حالة المصدر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if status == 'success':
                    cursor.execute('''
                        UPDATE news_sources
                        SET status = ?, last_check = CURRENT_TIMESTAMP,
                            last_success = CURRENT_TIMESTAMP, success_count = success_count + 1
                        WHERE id = ?
                    ''', (status, source_id))
                else:
                    cursor.execute('''
                        UPDATE news_sources
                        SET status = ?, last_check = CURRENT_TIMESTAMP,
                            error_count = error_count + 1
                        WHERE id = ?
                    ''', (f"{status}: {error_msg}" if error_msg else status, source_id))

                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث حالة المصدر: {str(e)}")
            return False

    def update_source_news_count(self, source_name: str, count: int) -> bool:
        """تحديث عدد الأخبار المجمعة من المصدر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE news_sources
                    SET total_news_collected = total_news_collected + ?
                    WHERE name = ?
                ''', (count, source_name))

                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث عدد الأخبار: {str(e)}")
            return False

    def toggle_source_status(self, source_id: int) -> bool:
        """تبديل حالة تفعيل المصدر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE news_sources
                    SET is_active = NOT is_active, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (source_id,))

                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"❌ خطأ في تبديل حالة المصدر: {str(e)}")
            return False

    def get_sources_stats(self) -> Dict:
        """جلب إحصائيات المصادر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # إجمالي المصادر
                cursor.execute('SELECT COUNT(*) FROM news_sources')
                total_sources = cursor.fetchone()[0]

                # المصادر النشطة
                cursor.execute('SELECT COUNT(*) FROM news_sources WHERE is_active = 1')
                active_sources = cursor.fetchone()[0]

                # المصادر المعطلة
                cursor.execute('SELECT COUNT(*) FROM news_sources WHERE is_active = 0')
                inactive_sources = cursor.fetchone()[0]

                # المصادر حسب النوع
                cursor.execute('''
                    SELECT type, COUNT(*) as count
                    FROM news_sources
                    GROUP BY type
                    ORDER BY count DESC
                ''')
                types = [{'type': row[0], 'count': row[1]} for row in cursor.fetchall()]

                # المصادر حسب الحالة
                cursor.execute('''
                    SELECT
                        CASE
                            WHEN status = 'success' THEN 'نجح'
                            WHEN status LIKE 'error%' THEN 'خطأ'
                            ELSE 'غير معروف'
                        END as status_group,
                        COUNT(*) as count
                    FROM news_sources
                    GROUP BY status_group
                    ORDER BY count DESC
                ''')
                statuses = [{'status': row[0], 'count': row[1]} for row in cursor.fetchall()]

                return {
                    'total_sources': total_sources,
                    'active_sources': active_sources,
                    'inactive_sources': inactive_sources,
                    'types': types,
                    'statuses': statuses
                }

        except Exception as e:
            logger.error(f"❌ خطأ في جلب إحصائيات المصادر: {str(e)}")
            return {
                'total_sources': 0,
                'active_sources': 0,
                'inactive_sources': 0,
                'types': [],
                'statuses': []
            }
