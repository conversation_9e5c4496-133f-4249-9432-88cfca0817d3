# 🇮🇶 نظام أخبار العراق - iNews

نظام شامل ومتطور لجمع وعرض الأخبار العراقية من مصادر متعددة موثوقة.

## ✨ المميزات

- 📰 **جمع تلقائي للأخبار** من 24 مصدر عراقي موثوق
- 🔄 **تحديث مستمر** كل دقيقة
- 📡 **دعم RSS** ومواقع الويب
- 🎯 **فلترة ذكية** للأخبار العراقية فقط
- 📱 **واجهة متجاوبة** تعمل على جميع الأجهزة
- ⚙️ **إدارة متقدمة للمصادر** مع فحص الحالة
- 🏷️ **تصنيف تلقائي** للأخبار (سياسة، اقتصاد، أمن، صحة)
- 🔍 **بحث وفلترة** متقدمة
- 📊 **إحصائيات مفصلة** عن المصادر والأخبار

## 🚀 التثبيت والتشغيل

### الطريقة السريعة:
```bash
python start_inews.py
```

### الطريقة اليدوية:

1. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

2. **تشغيل التطبيق:**
```bash
python app.py
```

3. **فتح المتصفح على:**
```
http://localhost:5020
```

## 🌐 الصفحات المتاحة

| الرابط | الوصف |
|--------|--------|
| `/` | الصفحة الرئيسية |
| `/news` | عرض الأخبار مع الفلترة والبحث |
| `/sources` | إدارة المصادر الإخبارية |
| `/admin` | لوحة الإدارة المتقدمة |

## 📊 المصادر المدعومة (24 مصدر)

### ✅ المصادر الفعالة (13 مصدر):
- **شفق نيوز** - shafaq.com
- **وكالة المعلومة** - almaalomah.me
- **شبكة أخبار الناصرية** - nasiriyah.org
- **شبكة الإعلام العراقي** - imn.iq
- **نون الخبرية** - nun.news
- **عراق أوبزيرفر** - observeriraq.net
- **الجبال نيوز** - aljeebal.com
- **UTV العراق** - utviraq.net
- **الأولى نيوز** - alawla.tv
- **شبكة أخبار العراق** - aliraqnews.com
- **المسلة** - almesalla.net
- **BBC عربي RSS** - feeds.bbci.co.uk
- **كتابات RSS** - kitabat.com/rss

### ⚠️ المصادر قيد التحسين (11 مصدر):
- **964 ميديا** - 964media.com
- **الفرات نيوز** - alforatnews.iq
- **السومرية نيوز** - alsumaria.tv
- **بغداد اليوم** - baghdadtoday.news
- **باس نيوز** - basnews.com
- **صحيفة الزمان** - azzaman.com
- **صحيفة المدى** - almadapaper.net
- **قناة الشرقية** - alsharqiya.net
- **كتابات في الميزان** - kitabat.com
- **هذا اليوم** - hathalyoum.net
- **سكاي نيوز عربية RSS** - skynewsarabia.com

## 📈 الإحصائيات

- **13 مصدر فعال** يجمع أخبار بنجاح
- **29+ خبر جديد** في كل دورة جمع
- **تحديث كل دقيقة** تلقائياً
- **فلترة ذكية** للأخبار العراقية فقط

## 📁 هيكل المشروع

```
inews/
├── app.py              # التطبيق الرئيسي
├── database.py         # إدارة قاعدة البيانات
├── news_scraper.py     # جامع الأخبار
├── source_manager.py   # إدارة المصادر
├── start_inews.py     # سكريبت التشغيل
├── requirements.txt    # المتطلبات
├── README.md          # هذا الملف
└── iraqi_news.db      # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🔧 الإدارة والصيانة

### فحص المصادر:
- يتم فحص المصادر تلقائياً
- يمكن فحص مصدر معين من صفحة `/sources`
- إحصائيات مفصلة عن حالة كل مصدر

### إدارة قاعدة البيانات:
- قاعدة بيانات SQLite محلية
- نسخ احتياطي تلقائي
- تنظيف الأخبار القديمة

### API Endpoints:
- `GET /api/news` - جلب جميع الأخبار
- `GET /api/news/today` - جلب أخبار اليوم
- `GET /api/news/stats` - إحصائيات الأخبار
- `POST /api/news/collect` - جمع الأخبار يدوياً
- `POST /api/news/clear` - حذف جميع الأخبار
- `GET /api/sources` - جلب المصادر
- `POST /api/sources/check` - فحص المصادر

## 🛠️ التطوير

### إضافة مصدر جديد:
1. إضافة المصدر من صفحة `/sources`
2. تحديد نوع المصدر (RSS أو موقع ويب)
3. إضافة محددات CSS للمواقع
4. فحص المصدر للتأكد من عمله

### الكلمات المفتاحية العراقية:
- أسماء المحافظات العراقية (بغداد، البصرة، أربيل...)
- المؤسسات الحكومية (وزارة، محافظة، مجلس النواب...)
- الشخصيات السياسية العراقية
- المصطلحات العراقية المحلية

### الكلمات المستبعدة:
- الفن والثقافة والترفيه
- الطقس والمناخ
- الرياضة والألعاب
- المحتوى غير الإخباري

## 🚀 الاستخدام

### جمع الأخبار يدوياً:
```bash
curl -X POST http://localhost:5020/api/news/collect
```

### جلب أخبار اليوم:
```bash
curl http://localhost:5020/api/news/today
```

### فحص المصادر:
```bash
curl -X POST http://localhost:5020/api/sources/check
```

## 📊 قاعدة البيانات

### جدول الأخبار (news):
- `id` - المعرف الفريد
- `title` - عنوان الخبر
- `content` - محتوى الخبر
- `summary` - ملخص الخبر
- `url` - رابط الخبر
- `source` - مصدر الخبر
- `category` - فئة الخبر
- `published_date` - تاريخ النشر
- `created_at` - تاريخ الإضافة
- `is_iraqi` - هل الخبر عراقي

### جدول المصادر (sources):
- `id` - المعرف الفريد
- `name` - اسم المصدر
- `url` - رابط المصدر
- `type` - نوع المصدر (rss/website)
- `is_active` - حالة التفعيل
- `selectors` - محددات CSS
- `status` - حالة المصدر
- `last_check` - آخر فحص

## 🔧 الإعدادات

- **المنطقة الزمنية**: `Asia/Baghdad`
- **جدولة المهام**: جمع الأخبار كل دقيقة
- **المنفذ**: 5020
- **قاعدة البيانات**: SQLite محلية

## 📈 المراقبة

- إحصائيات مباشرة في لوحة الإدارة
- تتبع عدد الأخبار المجمعة
- مراقبة حالة المصادر
- تقارير الأخطاء والنجاح

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة مصادر إخبارية جديدة
- تحسين واجهة المستخدم
- إصلاح الأخطاء
- تحسين الأداء

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الحر.

---

**🇮🇶 تم تطوير هذا النظام لخدمة المجتمع العراقي وتوفير مصدر موثوق للأخبار المحلية.**
