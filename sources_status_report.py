#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تقرير حالة المصادر الإخبارية العراقية
"""

from database import Database
import json

def generate_sources_report():
    """إنشاء تقرير شامل عن حالة المصادر"""
    db = Database()
    
    print("📊 تقرير حالة المصادر الإخبارية العراقية")
    print("=" * 60)
    
    # جلب جميع المصادر
    sources = db.get_all_sources()
    stats = db.get_sources_stats()
    
    print(f"\n📈 الإحصائيات العامة:")
    print(f"   📊 إجمالي المصادر: {stats['total_sources']}")
    print(f"   ✅ المصادر النشطة: {stats['active_sources']}")
    print(f"   ⏸️ المصادر المعطلة: {stats['inactive_sources']}")
    
    # إحصائيات الأنواع
    print(f"\n📡 أنواع المصادر:")
    for type_info in stats['types']:
        print(f"   {type_info['type']}: {type_info['count']} مصدر")
    
    # إحصائيات الحالة
    print(f"\n🔍 حالة المصادر:")
    for status_info in stats['statuses']:
        print(f"   {status_info['status']}: {status_info['count']} مصدر")
    
    # قائمة المصادر النشطة
    print(f"\n✅ المصادر النشطة ({stats['active_sources']}):")
    active_sources = [s for s in sources if s['is_active']]
    
    for i, source in enumerate(active_sources, 1):
        status_emoji = "✅" if source.get('status') == 'success' else "⚠️" if source.get('status') and 'error' not in source['status'] else "❌"
        type_emoji = "📡" if source['type'] == 'rss' else "🌐"
        
        print(f"   {i:2d}. {status_emoji} {type_emoji} {source['name']}")
        print(f"       🔗 {source['url']}")
        if source.get('status'):
            print(f"       📊 الحالة: {source['status']}")
        if source.get('last_check'):
            print(f"       🕐 آخر فحص: {source['last_check']}")
        print()
    
    # قائمة المصادر المعطلة
    if stats['inactive_sources'] > 0:
        print(f"\n⏸️ المصادر المعطلة ({stats['inactive_sources']}):")
        inactive_sources = [s for s in sources if not s['is_active']]
        
        for i, source in enumerate(inactive_sources, 1):
            type_emoji = "📡" if source['type'] == 'rss' else "🌐"
            print(f"   {i:2d}. ⏸️ {type_emoji} {source['name']}")
            print(f"       🔗 {source['url']}")
            if source.get('status'):
                print(f"       📊 الحالة: {source['status']}")
            print()
    
    # المصادر التي تعمل بنجاح
    working_sources = [s for s in sources if s.get('status') == 'success']
    print(f"\n🎉 المصادر التي تعمل بنجاح ({len(working_sources)}):")
    for source in working_sources:
        type_emoji = "📡" if source['type'] == 'rss' else "🌐"
        print(f"   ✅ {type_emoji} {source['name']}")
    
    # المصادر التي تحتاج إصلاح
    problematic_sources = [s for s in sources if s.get('status') and ('error' in s['status'] or 'HTTP' in s['status'])]
    if problematic_sources:
        print(f"\n🔧 المصادر التي تحتاج إصلاح ({len(problematic_sources)}):")
        for source in problematic_sources:
            type_emoji = "📡" if source['type'] == 'rss' else "🌐"
            print(f"   ❌ {type_emoji} {source['name']}")
            print(f"       🔗 {source['url']}")
            print(f"       ⚠️ المشكلة: {source['status']}")
            print()
    
    # توصيات
    print(f"\n💡 التوصيات:")
    
    if len(working_sources) >= 10:
        print("   ✅ لديك عدد جيد من المصادر التي تعمل")
    else:
        print("   ⚠️ يُنصح بإضافة المزيد من المصادر الموثوقة")
    
    if len(problematic_sources) > 0:
        print(f"   🔧 يوجد {len(problematic_sources)} مصدر يحتاج إصلاح")
    
    if stats['inactive_sources'] > 0:
        print(f"   ⏸️ يوجد {stats['inactive_sources']} مصدر معطل يمكن إعادة تفعيله")
    
    # نصائح للتحسين
    print(f"\n🚀 نصائح للتحسين:")
    print("   1. فحص المصادر دورياً للتأكد من عملها")
    print("   2. تحديث محددات CSS للمواقع التي تغير تصميمها")
    print("   3. إضافة مصادر RSS عندما تكون متوفرة (أسرع وأكثر موثوقية)")
    print("   4. مراقبة جودة الأخبار المجمعة من كل مصدر")
    print("   5. تعطيل المصادر التي لا تعمل لفترة طويلة")
    
    print(f"\n" + "=" * 60)
    print("📊 انتهى التقرير")

if __name__ == '__main__':
    generate_sources_report()
