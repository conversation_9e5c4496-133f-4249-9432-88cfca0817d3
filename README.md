# 🇮🇶 نظام أخبار العراق - Iraqi News System

نظام متطور لجمع وعرض الأخبار العراقية باستخدام Python و Flask

## ✨ المميزات

- 🎯 **تركيز على أخبار العراق فقط** - جمع الأخبار العراقية حصرياً
- 📅 **أخبار اليوم** - عرض أخبار اليوم الحالي بالتوقيت العراقي
- 🚫 **تصفية ذكية** - استبعاد أخبار الفن والثقافة والطقس والرياضة
- 🔄 **تحديث تلقائي** - جمع الأخبار كل 30 دقيقة
- 📱 **تصميم متجاوب** - يعمل على جميع الأجهزة
- 🗑️ **إدارة سهلة** - أدوات تنظيف وإدارة الأخبار
- 📊 **إحصائيات مفصلة** - تتبع المصادر والفئات

## 🛠️ التقنيات المستخدمة

- **Python 3.8+**
- **Flask** - إطار عمل الويب
- **SQLite** - قاعدة البيانات
- **BeautifulSoup** - تحليل HTML
- **Feedparser** - قراءة RSS feeds
- **Requests** - طلبات HTTP
- **Schedule** - جدولة المهام

## 📦 التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق

```bash
python app.py
```

### 3. فتح التطبيق

افتح المتصفح واذهب إلى: http://localhost:5020

## 📁 هيكل المشروع

```
iraqi-news-system/
├── app.py              # الملف الرئيسي للتطبيق
├── database.py         # إدارة قاعدة البيانات
├── news_scraper.py     # جامع الأخبار
├── requirements.txt    # المكتبات المطلوبة
├── README.md          # التوثيق
└── iraqi_news.db      # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🌐 المسارات المتاحة

### صفحات الويب
- `/` - الصفحة الرئيسية
- `/news` - عرض الأخبار
- `/admin` - لوحة الإدارة

### API Endpoints
- `GET /api/news` - جلب جميع الأخبار
- `GET /api/news/today` - جلب أخبار اليوم
- `GET /api/news/stats` - إحصائيات الأخبار
- `POST /api/news/collect` - جمع الأخبار يدوياً
- `POST /api/news/clear` - حذف جميع الأخبار

## 🎯 مصادر الأخبار

### مصادر RSS
- السومرية نيوز
- شفق نيوز
- رووداو

### الكلمات المفتاحية العراقية
- أسماء المحافظات العراقية
- المؤسسات الحكومية
- الكلمات المرتبطة بالعراق

### الكلمات المستبعدة
- الفن والثقافة
- الطقس والمناخ
- الرياضة
- الترفيه

## 📊 قاعدة البيانات

### جدول الأخبار (news)
- `id` - المعرف الفريد
- `title` - عنوان الخبر
- `content` - محتوى الخبر
- `summary` - ملخص الخبر
- `url` - رابط الخبر
- `source` - مصدر الخبر
- `category` - فئة الخبر
- `published_date` - تاريخ النشر
- `created_at` - تاريخ الإضافة
- `is_iraqi` - هل الخبر عراقي
- `keywords` - الكلمات المفتاحية

## 🔧 الإعدادات

### المنطقة الزمنية
- التوقيت العراقي: `Asia/Baghdad`

### جدولة المهام
- جمع الأخبار: كل 30 دقيقة
- تنظيف السجلات: يدوي

### المنفذ
- المنفذ الافتراضي: 5020

## 🚀 الاستخدام

### جمع الأخبار يدوياً
```bash
curl -X POST http://localhost:5020/api/news/collect
```

### جلب أخبار اليوم
```bash
curl http://localhost:5020/api/news/today
```

### حذف جميع الأخبار
```bash
curl -X POST http://localhost:5020/api/news/clear
```

## 📝 السجلات

يتم حفظ السجلات في ملف `iraqi_news.log` مع تفاصيل:
- عمليات جمع الأخبار
- الأخطاء والتحذيرات
- إحصائيات الأداء

## 🔒 الأمان

- تصفية المحتوى الضار
- حماية من SQL Injection
- تحديد معدل الطلبات
- تشفير الاتصالات (HTTPS في الإنتاج)

## 📈 المراقبة

- إحصائيات مباشرة في لوحة الإدارة
- تتبع عدد الأخبار المجمعة
- مراقبة حالة المصادر
- تقارير الأخطاء

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT

## 📞 الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- مراجعة التوثيق
- فحص ملف السجلات

---

**تم التطوير بـ ❤️ للمجتمع العراقي**
