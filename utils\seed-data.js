const News = require('../models/News');

// Sample Iraqi news data for testing
const sampleNews = [
  {
    title: 'رئيس الوزراء العراقي يبحث مع الوفد الأمريكي تعزيز العلاقات الثنائية',
    content: 'بحث رئيس مجلس الوزراء محمد شياع السوداني، اليوم الثلاثاء، مع الوفد الأمريكي رفيع المستوى تعزيز العلاقات الثنائية بين البلدين في مختلف المجالات. وذكر بيان لمكتب رئيس الوزراء أن السوداني استقبل في مكتبه الوفد الأمريكي برئاسة مساعد وزير الخارجية الأمريكي لشؤون الشرق الأدنى، حيث جرى خلال اللقاء بحث سبل تطوير العلاقات بين العراق والولايات المتحدة الأمريكية.',
    summary: 'رئيس الوزراء العراقي يستقبل وفداً أمريكياً لبحث تعزيز العلاقات الثنائية',
    url: 'https://example.com/news/1',
    source: 'shafaq',
    category: 'politics',
    author: 'شفق نيوز',
    published_date: '2024-01-15 10:30:00',
    image_url: 'https://example.com/images/pm-meeting.jpg',
    tags: 'العراق,السوداني,أمريكا,سياسة'
  },
  {
    title: 'البنك المركزي العراقي يعلن عن إجراءات جديدة لتنظيم السوق المصرفية',
    content: 'أعلن البنك المركزي العراقي عن مجموعة من الإجراءات الجديدة الهادفة إلى تنظيم السوق المصرفية وتعزيز الاستقرار المالي في البلاد. وتشمل هذه الإجراءات تشديد الرقابة على المصارف الأهلية وتحديث الأنظمة المصرفية الإلكترونية. كما أكد البنك على أهمية تطبيق المعايير الدولية في العمل المصرفي.',
    summary: 'البنك المركزي يعلن إجراءات جديدة لتنظيم السوق المصرفية',
    url: 'https://example.com/news/2',
    source: 'hathalyoum',
    category: 'economy',
    author: 'هذا اليوم',
    published_date: '2024-01-15 09:15:00',
    image_url: 'https://example.com/images/central-bank.jpg',
    tags: 'البنك المركزي,اقتصاد,مصارف'
  },
  {
    title: 'القوات الأمنية تنفذ عملية أمنية واسعة في محافظة الأنبار',
    content: 'نفذت القوات الأمنية العراقية عملية أمنية واسعة في محافظة الأنبار استهدفت خلايا إرهابية نائمة. وأسفرت العملية عن اعتقال عدد من المطلوبين وضبط كميات من الأسلحة والذخائر. وأكدت قيادة العمليات المشتركة أن العملية تأتي ضمن الجهود المستمرة لتأمين المحافظة ومنع تسلل العناصر الإرهابية.',
    summary: 'عملية أمنية واسعة في الأنبار تسفر عن اعتقال مطلوبين',
    url: 'https://example.com/news/3',
    source: 'media964',
    category: 'security',
    author: '964 ميديا',
    published_date: '2024-01-15 08:45:00',
    image_url: 'https://example.com/images/security-operation.jpg',
    tags: 'أمن,الأنبار,عمليات'
  },
  {
    title: 'منتخب العراق لكرة القدم يستعد لمواجهة منتخب الأردن في تصفيات كأس آسيا',
    content: 'يستعد منتخب العراق لكرة القدم لمواجهة نظيره الأردني في إطار تصفيات كأس آسيا المقررة الأسبوع المقبل. وأجرى المنتخب العراقي تدريباته الأخيرة في بغداد تحت إشراف الجهاز الفني. وأعرب المدرب عن ثقته في قدرة اللاعبين على تحقيق نتيجة إيجابية في هذه المواجهة المهمة.',
    summary: 'منتخب العراق يستعد لمواجهة الأردن في تصفيات كأس آسيا',
    url: 'https://example.com/news/4',
    source: 'shafaq',
    category: 'sports',
    author: 'شفق نيوز',
    published_date: '2024-01-15 07:20:00',
    image_url: 'https://example.com/images/iraq-team.jpg',
    tags: 'كرة القدم,منتخب العراق,كأس آسيا'
  },
  {
    title: 'وزارة الصحة تعلن عن حملة تطعيم واسعة ضد الأمراض الموسمية',
    content: 'أعلنت وزارة الصحة العراقية عن إطلاق حملة تطعيم واسعة ضد الأمراض الموسمية تستهدف جميع المحافظات. وتشمل الحملة التطعيم ضد الإنفلونزا الموسمية وأمراض أخرى. وأكدت الوزارة توفر اللقاحات اللازمة في جميع المراكز الصحية والمستشفيات الحكومية.',
    summary: 'وزارة الصحة تطلق حملة تطعيم واسعة ضد الأمراض الموسمية',
    url: 'https://example.com/news/5',
    source: 'hathalyoum',
    category: 'health',
    author: 'هذا اليوم',
    published_date: '2024-01-15 06:30:00',
    image_url: 'https://example.com/images/vaccination.jpg',
    tags: 'صحة,تطعيم,وزارة الصحة'
  },
  {
    title: 'جامعة بغداد تطلق برنامجاً جديداً للذكاء الاصطناعي',
    content: 'أطلقت جامعة بغداد برنامجاً أكاديمياً جديداً متخصصاً في الذكاء الاصطناعي وعلوم الحاسوب. ويهدف البرنامج إلى إعداد كوادر متخصصة في مجال التكنولوجيا المتقدمة. وأكد رئيس الجامعة أن هذا البرنامج يأتي ضمن خطة تطوير التعليم العالي في العراق ومواكبة التطورات التكنولوجية العالمية.',
    summary: 'جامعة بغداد تطلق برنامجاً جديداً للذكاء الاصطناعي',
    url: 'https://example.com/news/6',
    source: 'media964',
    category: 'technology',
    author: '964 ميديا',
    published_date: '2024-01-15 05:45:00',
    image_url: 'https://example.com/images/university-ai.jpg',
    tags: 'تعليم,ذكاء اصطناعي,جامعة بغداد'
  },
  {
    title: 'افتتاح مهرجان بابل الثقافي الدولي بمشاركة فنانين من عدة دول',
    content: 'افتتح في محافظة بابل مهرجان بابل الثقافي الدولي بمشاركة فنانين ومثقفين من عدة دول عربية وأجنبية. ويتضمن المهرجان فعاليات متنوعة تشمل العروض المسرحية والموسيقية والمعارض الفنية. وأكد محافظ بابل أن هذا المهرجان يهدف إلى إبراز التراث الثقافي العراقي وتعزيز التبادل الثقافي مع الدول الأخرى.',
    summary: 'افتتاح مهرجان بابل الثقافي الدولي بمشاركة دولية واسعة',
    url: 'https://example.com/news/7',
    source: 'shafaq',
    category: 'culture',
    author: 'شفق نيوز',
    published_date: '2024-01-14 20:15:00',
    image_url: 'https://example.com/images/babel-festival.jpg',
    tags: 'ثقافة,مهرجان,بابل,فنون'
  },
  {
    title: 'ارتفاع أسعار النفط العراقي في الأسواق العالمية',
    content: 'شهدت أسعار النفط العراقي ارتفاعاً ملحوظاً في الأسواق العالمية خلال تداولات اليوم. وبلغ سعر برميل النفط العراقي 85 دولاراً أمريكياً، مسجلاً زيادة قدرها 3% مقارنة بالأسبوع الماضي. وأرجع خبراء اقتصاديون هذا الارتفاع إلى تحسن الطلب العالمي على النفط وانخفاض المخزونات.',
    summary: 'أسعار النفط العراقي ترتفع 3% في الأسواق العالمية',
    url: 'https://example.com/news/8',
    source: 'hathalyoum',
    category: 'economy',
    author: 'هذا اليوم',
    published_date: '2024-01-14 18:30:00',
    image_url: 'https://example.com/images/oil-prices.jpg',
    tags: 'نفط,اقتصاد,أسعار'
  }
];

// Function to seed the database with sample data
async function seedDatabase() {
  console.log('🌱 بدء إدراج البيانات التجريبية...');
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const newsData of sampleNews) {
    try {
      const news = new News(newsData);
      await news.save();
      successCount++;
      console.log(`✅ تم إدراج: ${newsData.title.substring(0, 50)}...`);
    } catch (error) {
      errorCount++;
      console.error(`❌ خطأ في إدراج: ${newsData.title.substring(0, 50)}...`);
      console.error(`   السبب: ${error.message}`);
    }
  }
  
  console.log(`\n📊 ملخص إدراج البيانات التجريبية:`);
  console.log(`✅ تم إدراج: ${successCount} خبر`);
  console.log(`❌ فشل في إدراج: ${errorCount} خبر`);
  console.log(`📰 إجمالي البيانات: ${sampleNews.length} خبر\n`);
  
  return { success: successCount, errors: errorCount, total: sampleNews.length };
}

// Function to clear all news data
async function clearDatabase() {
  console.log('🗑️ بدء مسح جميع البيانات...');
  
  try {
    const { getDatabase } = require('../config/database');
    const db = getDatabase();
    
    return new Promise((resolve, reject) => {
      db.run('DELETE FROM news', [], function(err) {
        if (err) {
          console.error('❌ خطأ في مسح البيانات:', err.message);
          reject(err);
        } else {
          console.log(`✅ تم مسح ${this.changes} خبر من قاعدة البيانات`);
          resolve({ deleted: this.changes });
        }
      });
    });
  } catch (error) {
    console.error('❌ خطأ في مسح البيانات:', error.message);
    throw error;
  }
}

// Function to reset database with fresh sample data
async function resetDatabase() {
  console.log('🔄 بدء إعادة تعيين قاعدة البيانات...');
  
  try {
    await clearDatabase();
    const result = await seedDatabase();
    
    console.log('🎉 تم إعادة تعيين قاعدة البيانات بنجاح!');
    return result;
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين قاعدة البيانات:', error.message);
    throw error;
  }
}

module.exports = {
  seedDatabase,
  clearDatabase,
  resetDatabase,
  sampleNews
};
