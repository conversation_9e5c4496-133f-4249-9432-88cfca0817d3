const axios = require('axios');
const cheerio = require('cheerio');
const moment = require('moment-timezone');
const News = require('../models/News');
const { makeRequest, cleanArabicText, detectCategoryFromText, globalRateLimiter } = require('../utils/scraper-helpers');

class RSSNewsScraper {
  constructor() {
    this.source = 'rss-feeds';
    this.timezone = 'Asia/Baghdad';
    
    // RSS feeds from Iraqi news sources
    this.rssFeeds = [
      {
        name: 'aljazeera-arabic',
        url: 'https://www.aljazeera.net/xml/rss/all.xml',
        source: 'aljazeera'
      },
      {
        name: 'bbc-arabic',
        url: 'https://feeds.bbci.co.uk/arabic/rss.xml',
        source: 'bbc-arabic'
      },
      {
        name: 'skynews-arabic',
        url: 'https://www.skynewsarabia.com/rss',
        source: 'skynews-arabic'
      }
    ];
  }

  // Main scraping function
  async scrapeNews() {
    try {
      console.log('🔄 بدء جمع الأخبار من RSS feeds...');
      
      let totalSaved = 0;
      let totalErrors = 0;

      for (const feed of this.rssFeeds) {
        try {
          const { saved, errors } = await this.scrapeFeed(feed);
          totalSaved += saved;
          totalErrors += errors;
        } catch (error) {
          console.error(`❌ خطأ في جمع ${feed.name}:`, error.message);
          totalErrors++;
        }
      }

      console.log(`✅ RSS Feeds: تم حفظ ${totalSaved} خبر، ${totalErrors} خطأ`);
      return { saved: totalSaved, errors: totalErrors };
    } catch (error) {
      console.error('❌ خطأ في جمع أخبار RSS:', error.message);
      throw error;
    }
  }

  // Scrape a specific RSS feed
  async scrapeFeed(feed) {
    try {
      console.log(`🔄 جمع الأخبار من ${feed.name}...`);
      
      await globalRateLimiter.waitIfNeeded();
      const response = await makeRequest(feed.url, {
        headers: {
          'Accept': 'application/rss+xml, application/xml, text/xml'
        }
      });

      const $ = cheerio.load(response.data, { xmlMode: true });
      const articles = [];

      // Parse RSS items
      $('item').each((_, element) => {
        const $item = $(element);
        
        const title = $item.find('title').text().trim();
        const link = $item.find('link').text().trim();
        const description = $item.find('description').text().trim();
        const pubDate = $item.find('pubDate').text().trim();
        const category = $item.find('category').text().trim();

        if (title && link) {
          articles.push({
            title: title,
            url: link,
            summary: description,
            publishedDate: pubDate,
            category: category,
            source: feed.source
          });
        }
      });

      console.log(`📰 ${feed.name}: تم العثور على ${articles.length} مقال`);

      let savedCount = 0;
      let errorCount = 0;

      // Process articles
      for (const article of articles.slice(0, 10)) { // Limit to 10 per feed
        try {
          // Filter for Iraq-related news
          if (this.isIraqRelated(article.title + ' ' + article.summary)) {
            const processedArticle = await this.processArticle(article);
            if (processedArticle) {
              const news = new News(processedArticle);
              await news.save();
              savedCount++;
              console.log(`✅ تم حفظ: ${processedArticle.title.substring(0, 50)}...`);
            }
          }
        } catch (error) {
          errorCount++;
          console.error(`❌ خطأ في حفظ المقال: ${error.message}`);
        }
      }

      return { saved: savedCount, errors: errorCount };
    } catch (error) {
      console.error(`خطأ في جمع ${feed.name}:`, error.message);
      return { saved: 0, errors: 1 };
    }
  }

  // Check if article is Iraq-related
  isIraqRelated(text) {
    const iraqKeywords = [
      'العراق', 'عراق', 'بغداد', 'البصرة', 'الموصل', 'أربيل', 'كربلاء', 'النجف',
      'الأنبار', 'ديالى', 'صلاح الدين', 'كركوك', 'واسط', 'ميسان', 'ذي قار',
      'المثنى', 'بابل', 'دهوك', 'السليمانية', 'حلبجة', 'iraq', 'iraqi',
      'baghdad', 'basra', 'mosul', 'erbil', 'karbala', 'najaf'
    ];

    const lowerText = text.toLowerCase();
    return iraqKeywords.some(keyword => lowerText.includes(keyword.toLowerCase()));
  }

  // Process and clean article data
  async processArticle(article) {
    try {
      // Parse published date
      let publishedDate = moment.tz(this.timezone).format('YYYY-MM-DD HH:mm:ss');
      if (article.publishedDate) {
        const parsed = moment.tz(article.publishedDate, this.timezone);
        if (parsed.isValid()) {
          publishedDate = parsed.format('YYYY-MM-DD HH:mm:ss');
        }
      }

      // Determine category
      let category = 'general';
      if (article.category) {
        category = this.mapCategory(article.category);
      } else {
        category = detectCategoryFromText(article.title + ' ' + article.summary);
      }

      // Try to get full content if possible
      let content = article.summary || '';
      try {
        if (article.url && !article.url.includes('javascript:')) {
          await globalRateLimiter.waitIfNeeded();
          const pageResponse = await makeRequest(article.url);
          const $page = cheerio.load(pageResponse.data);
          
          const fullContent = this.extractContent($page);
          if (fullContent && fullContent.length > content.length) {
            content = fullContent;
          }
        }
      } catch (error) {
        // Use summary if full content extraction fails
        console.log(`تعذر جلب المحتوى الكامل من ${article.url}`);
      }

      return {
        title: cleanArabicText(article.title),
        content: cleanArabicText(content),
        summary: cleanArabicText(article.summary) || cleanArabicText(content).substring(0, 200) + '...',
        url: article.url,
        source: article.source,
        category: category,
        author: article.source,
        published_date: publishedDate,
        image_url: null,
        tags: this.extractTags(article.title + ' ' + content)
      };
    } catch (error) {
      console.error(`خطأ في معالجة المقال: ${error.message}`);
      return null;
    }
  }

  // Extract content from full article page
  extractContent($) {
    const contentSelectors = [
      '.article-content',
      '.post-content',
      '.entry-content',
      '.content',
      '.story-content',
      '.news-content',
      '.main-content',
      'article .content',
      '.post-body',
      '[data-module="ArticleBody"]',
      '.story-body__inner'
    ];

    for (const selector of contentSelectors) {
      const content = $(selector).text().trim();
      if (content && content.length > 100) {
        return content.replace(/\s+/g, ' ').trim();
      }
    }

    // Fallback: get all paragraphs
    const paragraphs = $('p').map((_, el) => $(el).text().trim()).get();
    const validParagraphs = paragraphs.filter(p => p.length > 20);
    return validParagraphs.slice(0, 10).join('\n\n'); // Limit to first 10 paragraphs
  }

  // Extract tags from text
  extractTags(text) {
    const commonTags = [
      'العراق', 'بغداد', 'سياسة', 'اقتصاد', 'أمن', 'رياضة', 'ثقافة',
      'تكنولوجيا', 'صحة', 'تعليم', 'نفط', 'استثمار', 'حكومة', 'برلمان'
    ];

    const foundTags = [];
    const lowerText = text.toLowerCase();

    for (const tag of commonTags) {
      if (lowerText.includes(tag.toLowerCase())) {
        foundTags.push(tag);
      }
    }

    return foundTags.length > 0 ? foundTags.join(',') : null;
  }

  // Map categories
  mapCategory(category) {
    const categoryMap = {
      'سياسة': 'politics',
      'اقتصاد': 'economy',
      'رياضة': 'sports',
      'ثقافة': 'culture',
      'تكنولوجيا': 'technology',
      'صحة': 'health',
      'politics': 'politics',
      'economy': 'economy',
      'business': 'economy',
      'sports': 'sports',
      'culture': 'culture',
      'technology': 'technology',
      'health': 'health',
      'science': 'technology'
    };

    const lowerCategory = category.toLowerCase();
    for (const [key, value] of Object.entries(categoryMap)) {
      if (lowerCategory.includes(key.toLowerCase())) {
        return value;
      }
    }

    return 'general';
  }
}

module.exports = RSSNewsScraper;
