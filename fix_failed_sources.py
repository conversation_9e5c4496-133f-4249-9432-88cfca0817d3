#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح المصادر الفاشلة تلقائياً
"""

import requests
import json
import time
from database import Database
from bs4 import BeautifulSoup

class SourcesFixer:
    def __init__(self):
        """تهيئة أداة الإصلاح"""
        self.db = Database()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ar,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
    
    def get_optimized_selectors(self, source_name, url):
        """الحصول على محددات محسنة للمواقع المختلفة"""
        
        # محددات مخصصة للمواقع العراقية المعروفة
        custom_selectors = {
            '964 ميديا': {
                'container': '.post, .news-post, .article-post, .content-post, .wire-post',
                'title': 'h1, h2, h3, .post-title, .entry-title, .title',
                'content': '.post-content, .entry-content, .content, p',
                'link': 'a'
            },
            'السومرية نيوز': {
                'container': '.news-block, .news-item, .article-block, .post-block, .content-block',
                'title': 'h1, h2, h3, .news-title, .article-title, .block-title',
                'content': '.news-content, .article-content, .block-content, p',
                'link': 'a'
            },
            'الفرات نيوز': {
                'container': '.news-card, .article-card, .post-card, .content-card, .item-card',
                'title': 'h1, h2, h3, .card-title, .news-title, .article-title',
                'content': '.card-content, .news-content, .article-content, p',
                'link': 'a'
            },
            'بغداد اليوم': {
                'container': '.news-row, .article-row, .post-row, .content-row, .item-row',
                'title': 'h1, h2, h3, .row-title, .news-title, .article-title',
                'content': '.row-content, .news-content, .article-content, p',
                'link': 'a'
            },
            'باس نيوز': {
                'container': '.news-list-item, .article-list-item, .post-list-item, .content-list-item',
                'title': 'h1, h2, h3, .item-title, .news-title, .article-title',
                'content': '.item-content, .news-content, .article-content, p',
                'link': 'a'
            },
            'صحيفة الزمان': {
                'container': '.news-section, .article-section, .post-section, .content-section',
                'title': 'h1, h2, h3, .section-title, .news-title, .article-title',
                'content': '.section-content, .news-content, .article-content, p',
                'link': 'a'
            },
            'صحيفة المدى': {
                'container': '.news-widget, .article-widget, .post-widget, .content-widget',
                'title': 'h1, h2, h3, .widget-title, .news-title, .article-title',
                'content': '.widget-content, .news-content, .article-content, p',
                'link': 'a'
            },
            'قناة الشرقية': {
                'container': '.news-container, .article-container, .post-container, .content-container',
                'title': 'h1, h2, h3, .container-title, .news-title, .article-title',
                'content': '.container-content, .news-content, .article-content, p',
                'link': 'a'
            },
            'كتابات في الميزان': {
                'container': '.post-item, .article-item, .news-item, .content-item, .entry-item',
                'title': 'h1, h2, h3, .item-title, .post-title, .entry-title',
                'content': '.item-content, .post-content, .entry-content, p',
                'link': 'a'
            },
            'نون الخبرية': {
                'container': 'article, .article, .news-article, .post-article',
                'title': 'a[title], h1, h2, h3, .article-title, .news-title',
                'content': '.article-content, .news-content, .post-content, p',
                'link': 'a'
            },
            'هذا اليوم': {
                'container': '.news-box, .article-box, .post-box, .content-box',
                'title': 'h1, h2, h3, .box-title, .news-title, .article-title',
                'content': '.box-content, .news-content, .article-content, p',
                'link': 'a'
            }
        }
        
        # إذا كان هناك محددات مخصصة
        if source_name in custom_selectors:
            return custom_selectors[source_name]
        
        # محددات عامة محسنة
        return {
            'container': 'article, .article, .news-item, .post, .entry, .news, .item, .story, .card, .content-item, .news-box, .post-item, .news-card, .article-item, .story-item, .news-story, .content-box, .item-news, .news-content, .post-content, .article-content, .main-news, .latest-news, .news-list, .articles, .posts, .entries, .stories, .content, .main',
            'title': 'h1, h2, h3, h4, h5, h6, .title, .headline, .post-title, .news-title, .article-title, .story-title, .entry-title, .item-title, .content-title, .news-headline, .post-headline, .article-headline, .story-headline, .entry-headline, .item-headline, .content-headline, a[title], .link-title, .news-link, .post-link, .article-link, .story-link, .entry-link, .item-link, .content-link, .main-title, .header-title, .primary-title',
            'content': 'p, .content, .summary, .excerpt, .description, .text, .body, .news-content, .post-content, .article-content, .story-content, .entry-content, .item-content, .news-text, .post-text, .article-text, .story-text, .entry-text, .item-text, .content-text, .news-summary, .post-summary, .article-summary, .story-summary, .entry-summary, .item-summary, .content-summary, .news-excerpt, .post-excerpt, .article-excerpt, .story-excerpt, .entry-excerpt, .item-excerpt, .content-excerpt',
            'link': 'a, .link, .news-link, .post-link, .article-link, .story-link, .entry-link, .item-link, .content-link, .title-link, .headline-link, .header-link, .read-more, .more-link, .continue-reading, .full-story, .details-link'
        }
    
    def test_and_find_best_selectors(self, url, source_name):
        """اختبار وإيجاد أفضل محددات للموقع"""
        try:
            response = self.session.get(url, timeout=15)
            if response.status_code != 200:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # قائمة محددات للاختبار
            container_tests = [
                '.news-item', '.post', 'article', '.entry', '.news', '.item', '.story', '.card',
                '.content-item', '.news-box', '.post-item', '.news-card', '.article-item',
                '.story-item', '.news-story', '.content-box', '.item-news', '.news-content',
                '.post-content', '.article-content', '.main-news', '.latest-news', '.news-list',
                '.articles', '.posts', '.entries', '.stories', '.content', '.main',
                '.news-block', '.article-block', '.post-block', '.content-block',
                '.news-row', '.article-row', '.post-row', '.content-row',
                '.news-section', '.article-section', '.post-section', '.content-section',
                '.news-widget', '.article-widget', '.post-widget', '.content-widget',
                '.news-container', '.article-container', '.post-container', '.content-container',
                '.news-box', '.article-box', '.post-box', '.content-box'
            ]
            
            title_tests = [
                'h1', 'h2', 'h3', '.title', '.headline', '.post-title', '.news-title',
                '.article-title', '.story-title', '.entry-title', '.item-title',
                '.content-title', '.news-headline', '.post-headline', '.article-headline',
                'a[title]', '.link-title', '.news-link', '.main-title', '.header-title'
            ]
            
            best_score = 0
            best_selectors = None
            
            for container in container_tests:
                containers = soup.select(container)
                if not containers or len(containers) < 2:
                    continue
                
                for title_sel in title_tests:
                    score = 0
                    valid_items = 0
                    
                    for cont in containers[:10]:
                        title_elem = cont.select_one(title_sel)
                        if title_elem and title_elem.get_text().strip():
                            valid_items += 1
                            score += len(title_elem.get_text().strip())
                    
                    if valid_items > best_score:
                        best_score = valid_items
                        best_selectors = {
                            'container': container,
                            'title': title_sel,
                            'content': 'p, .content, .summary, .excerpt, .description',
                            'link': 'a'
                        }
            
            return best_selectors if best_score > 2 else None
            
        except Exception as e:
            print(f"خطأ في اختبار المحددات: {str(e)}")
            return None
    
    def fix_source_selectors(self, source):
        """إصلاح محددات المصدر"""
        print(f"\n🔧 إصلاح محددات: {source['name']}")
        
        # 1. جرب المحددات المخصصة أولاً
        optimized_selectors = self.get_optimized_selectors(source['name'], source['url'])
        
        # 2. اختبر المحددات المخصصة
        try:
            response = self.session.get(source['url'], timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                containers = soup.select(optimized_selectors['container'])
                if containers and len(containers) >= 2:
                    # اختبر العناوين
                    valid_titles = 0
                    for container in containers[:5]:
                        title_elem = container.select_one(optimized_selectors['title'])
                        if title_elem and title_elem.get_text().strip():
                            valid_titles += 1
                    
                    if valid_titles >= 2:
                        print(f"   ✅ المحددات المخصصة تعمل: {valid_titles} عنوان صحيح")
                        return optimized_selectors
        except:
            pass
        
        # 3. إذا فشلت المحددات المخصصة، ابحث عن أفضل محددات
        print("   🔍 البحث عن محددات أفضل...")
        best_selectors = self.test_and_find_best_selectors(source['url'], source['name'])
        
        if best_selectors:
            print(f"   ✅ تم العثور على محددات أفضل")
            return best_selectors
        else:
            print(f"   ⚠️ استخدام محددات عامة")
            return optimized_selectors
    
    def update_source_selectors(self, source_id, new_selectors):
        """تحديث محددات المصدر في قاعدة البيانات"""
        try:
            update_data = {
                'selectors': json.dumps(new_selectors, ensure_ascii=False)
            }
            
            success = self.db.update_source(source_id, update_data)
            return success
        except Exception as e:
            print(f"خطأ في تحديث المحددات: {str(e)}")
            return False
    
    def fix_all_failed_sources(self):
        """إصلاح جميع المصادر الفاشلة"""
        print("🚀 بدء إصلاح المصادر الفاشلة...")
        
        # جلب المصادر الفاشلة
        sources = self.db.get_all_sources()
        failed_sources = []
        
        for source in sources:
            if (source.get('status') and 
                ('no_news' in source['status'] or 
                 'error' in source['status'] or 
                 source['status'] == 'غير معروف')):
                failed_sources.append(source)
        
        if not failed_sources:
            print("✅ لا توجد مصادر تحتاج إصلاح!")
            return
        
        print(f"📊 تم العثور على {len(failed_sources)} مصدر يحتاج إصلاح")
        
        fixed_count = 0
        failed_count = 0
        
        for source in failed_sources:
            try:
                print(f"\n🔧 إصلاح: {source['name']}")
                
                # إصلاح المحددات
                new_selectors = self.fix_source_selectors(source)
                
                if new_selectors:
                    # تحديث المحددات في قاعدة البيانات
                    if self.update_source_selectors(source['id'], new_selectors):
                        fixed_count += 1
                        print(f"   ✅ تم إصلاح المحددات بنجاح")
                        
                        # إعادة تعيين حالة المصدر
                        self.db.update_source_status(source['id'], 'unknown')
                    else:
                        failed_count += 1
                        print(f"   ❌ فشل في تحديث المحددات")
                else:
                    failed_count += 1
                    print(f"   ❌ لم يتم العثور على محددات مناسبة")
                
                time.sleep(1)  # توقف قصير
                
            except Exception as e:
                failed_count += 1
                print(f"   ❌ خطأ في إصلاح {source['name']}: {str(e)}")
        
        print(f"\n🎉 انتهى الإصلاح:")
        print(f"✅ تم إصلاح: {fixed_count} مصدر")
        print(f"❌ فشل في إصلاح: {failed_count} مصدر")
        print(f"📊 إجمالي المصادر المعالجة: {len(failed_sources)}")
        
        return fixed_count, failed_count
    
    def disable_problematic_sources(self):
        """تعطيل المصادر المشكلة التي لا يمكن إصلاحها"""
        print("\n⏸️ تعطيل المصادر المشكلة...")
        
        # قائمة المصادر التي تحتاج تعطيل مؤقت
        problematic_sources = [
            'قناة الشرقية'  # محتوى إخباري قليل جداً
        ]
        
        disabled_count = 0
        
        for source_name in problematic_sources:
            try:
                sources = self.db.get_sources_by_name(source_name)
                for source in sources:
                    if source['is_active']:
                        update_data = {'is_active': False}
                        if self.db.update_source(source['id'], update_data):
                            disabled_count += 1
                            print(f"⏸️ تم تعطيل: {source_name}")
                        else:
                            print(f"❌ فشل في تعطيل: {source_name}")
            except Exception as e:
                print(f"❌ خطأ في تعطيل {source_name}: {str(e)}")
        
        print(f"⏸️ تم تعطيل {disabled_count} مصدر مشكل")
        return disabled_count

if __name__ == '__main__':
    fixer = SourcesFixer()
    
    # إصلاح المصادر الفاشلة
    fixed, failed = fixer.fix_all_failed_sources()
    
    # تعطيل المصادر المشكلة
    disabled = fixer.disable_problematic_sources()
    
    print(f"\n🎊 النتيجة النهائية:")
    print(f"✅ مصادر تم إصلاحها: {fixed}")
    print(f"❌ مصادر فشل إصلاحها: {failed}")
    print(f"⏸️ مصادر تم تعطيلها: {disabled}")
    print(f"📊 إجمالي المصادر المعالجة: {fixed + failed + disabled}")
