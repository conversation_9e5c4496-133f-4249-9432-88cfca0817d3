const cron = require('node-cron');
const ShafaqScraper = require('../scrapers/shafaq');
const HathalyoumScraper = require('../scrapers/hathalyoum');
const Media964Scraper = require('../scrapers/media964');
const AlsumariaScraper = require('../scrapers/alsumaria');
const RSSNewsScraper = require('../scrapers/rss-scraper');
const IraqiRSSScraper = require('../scrapers/iraqi-rss');
const SimpleNewsScraper = require('../scrapers/simple-news');
const News = require('../models/News');

class NewsScheduler {
  constructor() {
    this.scrapers = {
      // Original scrapers (may have 403 issues)
      // shafaq: new ShafaqScraper(),
      // hathalyoum: new HathalyoumScraper(),
      // media964: new Media964Scraper(),
      // alsumaria: new AlsumariaScraper(),

      // Working scrapers
      rss: new RSSNewsScraper(),
      iraqiRss: new IraqiRSSScraper(),
      simpleNews: new SimpleNewsScraper()
    };
    
    this.isRunning = false;
    this.lastRun = null;
    this.stats = {
      totalRuns: 0,
      successfulRuns: 0,
      failedRuns: 0,
      totalArticlesSaved: 0
    };
  }

  // Start the scheduler
  start() {
    console.log('🕐 بدء تشغيل جدولة تحديث الأخبار...');

    // Run every 30 minutes
    cron.schedule('*/30 * * * *', async () => {
      await this.runAllScrapers();
    });

    // Run every 2 hours for cleanup
    cron.schedule('0 */2 * * *', async () => {
      await this.cleanupOldNews();
    });

    // Run immediately on start
    setTimeout(() => {
      this.runAllScrapers();
    }, 5000); // Wait 5 seconds after server start

    console.log('✅ تم تشغيل جدولة تحديث الأخبار');
  }

  // Run all scrapers
  async runAllScrapers() {
    if (this.isRunning) {
      console.log('⏳ جمع الأخبار قيد التشغيل بالفعل، تخطي هذه الدورة...');
      return;
    }

    this.isRunning = true;
    this.lastRun = new Date();
    this.stats.totalRuns++;

    console.log('🚀 بدء دورة جمع الأخبار الجديدة...');

    const results = {
      shafaq: { saved: 0, errors: 0 },
      hathalyoum: { saved: 0, errors: 0 },
      media964: { saved: 0, errors: 0 }
    };

    try {
      // Run scrapers in parallel with error handling
      const scraperPromises = Object.entries(this.scrapers).map(async ([name, scraper]) => {
        try {
          console.log(`🔄 تشغيل ${name}...`);
          const result = await scraper.scrapeNews();
          results[name] = result;
          console.log(`✅ ${name}: ${result.saved} مقال محفوظ، ${result.errors} خطأ`);
          return result;
        } catch (error) {
          console.error(`❌ خطأ في ${name}:`, error.message);
          results[name] = { saved: 0, errors: 1 };
          return { saved: 0, errors: 1 };
        }
      });

      await Promise.all(scraperPromises);

      // Calculate totals
      const totalSaved = Object.values(results).reduce((sum, result) => sum + result.saved, 0);
      const totalErrors = Object.values(results).reduce((sum, result) => sum + result.errors, 0);

      this.stats.totalArticlesSaved += totalSaved;
      
      if (totalErrors === 0) {
        this.stats.successfulRuns++;
      } else {
        this.stats.failedRuns++;
      }

      console.log(`🎉 انتهت دورة جمع الأخبار: ${totalSaved} مقال محفوظ، ${totalErrors} خطأ`);
      
      // Log summary
      this.logSummary(results);

    } catch (error) {
      console.error('❌ خطأ عام في دورة جمع الأخبار:', error.message);
      this.stats.failedRuns++;
    } finally {
      this.isRunning = false;
    }
  }

  // Run a specific scraper
  async runScraper(scraperName) {
    if (!this.scrapers[scraperName]) {
      throw new Error(`Scraper ${scraperName} not found`);
    }

    console.log(`🔄 تشغيل ${scraperName} يدوياً...`);
    
    try {
      const result = await this.scrapers[scraperName].scrapeNews();
      console.log(`✅ ${scraperName}: ${result.saved} مقال محفوظ، ${result.errors} خطأ`);
      return result;
    } catch (error) {
      console.error(`❌ خطأ في ${scraperName}:`, error.message);
      throw error;
    }
  }

  // Cleanup old news
  async cleanupOldNews() {
    try {
      console.log('🧹 بدء تنظيف الأخبار القديمة...');
      
      const result = await News.deleteOld(30); // Delete news older than 30 days
      
      if (result.changes > 0) {
        console.log(`🗑️ تم حذف ${result.changes} خبر قديم`);
      } else {
        console.log('✅ لا توجد أخبار قديمة للحذف');
      }
    } catch (error) {
      console.error('❌ خطأ في تنظيف الأخبار القديمة:', error.message);
    }
  }

  // Log summary of scraping results
  logSummary(results) {
    console.log('\n📊 ملخص دورة جمع الأخبار:');
    console.log('================================');
    
    Object.entries(results).forEach(([source, result]) => {
      console.log(`${source}: ${result.saved} محفوظ، ${result.errors} خطأ`);
    });
    
    console.log('================================');
    console.log(`⏰ وقت آخر تشغيل: ${this.lastRun.toLocaleString('ar-IQ')}`);
    console.log(`📈 إجمالي الدورات: ${this.stats.totalRuns}`);
    console.log(`✅ دورات ناجحة: ${this.stats.successfulRuns}`);
    console.log(`❌ دورات فاشلة: ${this.stats.failedRuns}`);
    console.log(`📰 إجمالي المقالات المحفوظة: ${this.stats.totalArticlesSaved}`);
    console.log('================================\n');
  }

  // Get scheduler status
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastRun: this.lastRun,
      stats: this.stats,
      nextRun: this.getNextRunTime()
    };
  }

  // Get next scheduled run time
  getNextRunTime() {
    if (!this.lastRun) return 'قريباً';
    
    const nextRun = new Date(this.lastRun.getTime() + 30 * 60 * 1000); // Add 30 minutes
    return nextRun.toLocaleString('ar-IQ');
  }

  // Manual trigger for all scrapers
  async triggerManualRun() {
    if (this.isRunning) {
      throw new Error('جمع الأخبار قيد التشغيل بالفعل');
    }

    console.log('🔧 تشغيل يدوي لجمع الأخبار...');
    await this.runAllScrapers();
  }

  // Get scraper statistics
  async getScraperStats() {
    try {
      const stats = await News.getStats();
      return {
        ...stats,
        scheduler: this.getStatus()
      };
    } catch (error) {
      console.error('خطأ في جلب إحصائيات الـ scrapers:', error.message);
      return {
        scheduler: this.getStatus()
      };
    }
  }
}

// Create singleton instance
const scheduler = new NewsScheduler();

// Export functions
function startScheduler() {
  scheduler.start();
}

function getSchedulerStatus() {
  return scheduler.getStatus();
}

function triggerManualRun() {
  return scheduler.triggerManualRun();
}

function runSpecificScraper(scraperName) {
  return scheduler.runScraper(scraperName);
}

function getScraperStats() {
  return scheduler.getScraperStats();
}

module.exports = {
  startScheduler,
  getSchedulerStatus,
  triggerManualRun,
  runSpecificScraper,
  getScraperStats
};
