const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const newsRoutes = require('./routes/news');
const { initDatabase } = require('./config/database');
const { startScheduler } = require('./utils/scheduler');

const app = express();
const PORT = process.env.PORT || 5020;

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً'
  }
});
app.use('/api/', limiter);

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/news', newsRoutes);

// Health check endpoint with HTML interface
app.get('/health', (req, res) => {
  const uptime = process.uptime();
  const uptimeFormatted = {
    days: Math.floor(uptime / 86400),
    hours: Math.floor((uptime % 86400) / 3600),
    minutes: Math.floor((uptime % 3600) / 60),
    seconds: Math.floor(uptime % 60)
  };

  const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص حالة الخادم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            background: #2ecc71;
            border-radius: 50%;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 30px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-right: 5px solid #27ae60;
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
        }
        .status-label {
            color: #666;
            font-size: 0.9em;
        }
        .system-info {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .refresh-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
        }
        .refresh-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فحص حالة الخادم</h1>
            <p>الخادم يعمل بشكل طبيعي <span class="status-indicator"></span></p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 الأخبار</a>
            <a href="/health">💚 حالة الخادم</a>
            <a href="/admin/system">⚙️ حالة النظام</a>
            <a href="/admin/trigger">🔄 تحديث الأخبار</a>
        </div>

        <div class="content">
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-value">✅ نشط</div>
                    <div class="status-label">حالة الخادم</div>
                </div>
                <div class="status-card">
                    <div class="status-value">${uptimeFormatted.days}د ${uptimeFormatted.hours}س</div>
                    <div class="status-label">مدة التشغيل</div>
                </div>
                <div class="status-card">
                    <div class="status-value">${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(1)} MB</div>
                    <div class="status-label">استخدام الذاكرة</div>
                </div>
                <div class="status-card">
                    <div class="status-value">5020</div>
                    <div class="status-label">منفذ الخادم</div>
                </div>
            </div>

            <div class="system-info">
                <h3>📊 معلومات النظام</h3>
                <div class="info-row">
                    <span>🕐 الوقت الحالي:</span>
                    <span>${new Date().toLocaleString('ar-EG', { timeZone: 'Asia/Baghdad' })}</span>
                </div>
                <div class="info-row">
                    <span>🖥️ إصدار Node.js:</span>
                    <span>${process.version}</span>
                </div>
                <div class="info-row">
                    <span>🏗️ المنصة:</span>
                    <span>${process.platform}</span>
                </div>
                <div class="info-row">
                    <span>⚡ معرف العملية:</span>
                    <span>${process.pid}</span>
                </div>
                <div class="info-row">
                    <span>📈 مدة التشغيل التفصيلية:</span>
                    <span>${uptimeFormatted.days} يوم، ${uptimeFormatted.hours} ساعة، ${uptimeFormatted.minutes} دقيقة، ${uptimeFormatted.seconds} ثانية</span>
                </div>
            </div>

            <button class="refresh-btn" onclick="window.location.reload()">🔄 تحديث الصفحة</button>

            <div class="timestamp">
                آخر تحديث: ${new Date().toLocaleString('ar-EG')}
            </div>
        </div>
    </div>

    <script>
        // Auto refresh every 30 seconds
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>`;

  res.send(htmlContent);
});

// Root endpoint with HTML interface
app.get('/', (req, res) => {
  const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API الأخبار العراقية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .endpoints {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .endpoint {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .endpoint:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .endpoint-url {
            background: #2c3e50;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .endpoint-desc {
            color: #666;
            font-size: 0.95em;
        }
        .sources {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .source {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        .stats {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-top: 20px;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 5px;
            transition: transform 0.3s ease;
            font-weight: bold;
        }
        .btn:hover {
            transform: scale(1.05);
        }
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇮🇶 API الأخبار العراقية</h1>
            <p>الإصدار 1.0.0 - مصدرك الموثوق للأخبار العراقية</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>📡 نقاط النهاية المتاحة</h2>
                <div class="endpoints">
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news</div>
                        <div class="endpoint-desc">جلب جميع الأخبار مع إمكانية التصفية والترقيم</div>
                        <a href="/api/news?limit=5" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/latest</div>
                        <div class="endpoint-desc">أحدث الأخبار من جميع المصادر</div>
                        <a href="/api/news/latest" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/search/:query</div>
                        <div class="endpoint-desc">البحث في الأخبار باستخدام كلمات مفتاحية</div>
                        <a href="/api/news/search/العراق" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/category/:category</div>
                        <div class="endpoint-desc">أخبار حسب الفئة (سياسة، اقتصاد، رياضة، إلخ)</div>
                        <a href="/api/news/category/politics" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/source/:source</div>
                        <div class="endpoint-desc">أخبار من مصدر محدد</div>
                        <a href="/api/news/source/bbc-arabic" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/stats</div>
                        <div class="endpoint-desc">إحصائيات مفصلة عن الأخبار والمصادر</div>
                        <a href="/api/news/stats" class="btn">جرب الآن</a>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📰 المصادر المدعومة</h2>
                <div class="sources">
                    <div class="source">BBC العربية</div>
                    <div class="source">سكاي نيوز العربية</div>
                    <div class="source">الأخبار العراقية</div>
                    <div class="source">مصادر RSS متنوعة</div>
                </div>
            </div>

            <div class="section">
                <h2>📱 صفحات عرض الأخبار</h2>
                <a href="/news" class="btn">📰 جميع الأخبار</a>
                <a href="/news/today" class="btn">📅 أخبار اليوم</a>
                <a href="/news/category/politics" class="btn">🏛️ الأخبار السياسية</a>
                <a href="/news/category/economy" class="btn">💰 الأخبار الاقتصادية</a>
            </div>

            <div class="section">
                <h2>🔧 أدوات الإدارة</h2>
                <a href="/health" class="btn">💚 فحص حالة الخادم</a>
                <a href="/admin/system" class="btn">⚙️ حالة النظام</a>
                <a href="/admin/trigger" class="btn">🔄 تحديث الأخبار يدوياً</a>
                <a href="/admin/clear" class="btn">🗑️ تنظيف الأخبار</a>
            </div>

            <div class="stats">
                <h3>📊 إحصائيات سريعة</h3>
                <p>API يعمل بكامل طاقته • تحديث تلقائي كل 30 دقيقة • أخبار فعلية من مصادر موثوقة</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 API الأخبار العراقية - تم التطوير بواسطة Augment Agent</p>
        </div>
    </div>
</body>
</html>`;

  res.send(htmlContent);
});

// News display page
app.get('/news', async (req, res) => {
  try {
    const { getDatabase } = require('./config/database');
    const db = getDatabase();
    const today = new Date().toISOString().split('T')[0];

    // Get today's news
    const todayNews = await new Promise((resolve, reject) => {
      db.all(`
        SELECT * FROM news
        WHERE DATE(published_date) = ?
        ORDER BY published_date DESC
        LIMIT 20
      `, [today], (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });

    // Get all recent news if no today's news
    const allNews = await new Promise((resolve, reject) => {
      db.all(`
        SELECT * FROM news
        ORDER BY published_date DESC
        LIMIT 50
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });

    const newsToShow = todayNews.length > 0 ? todayNews : allNews.slice(0, 20);

    const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأخبار العراقية - ${today}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header .date {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .nav {
            background: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex-wrap: wrap;
            gap: 10px;
        }
        .nav a {
            color: #2c3e50;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .nav a:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
        }
        .nav-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .stats {
            background: #e74c3c;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
        }
        .clear-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }
        .clear-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        .clear-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .news-container {
            background: white;
            border-radius: 0 0 15px 15px;
            min-height: 500px;
        }
        .news-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            padding: 30px;
        }
        .news-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-right: 5px solid #3498db;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            display: block;
        }
        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
            border-right-color: #e74c3c;
        }
        .news-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }
        .news-source {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.85em;
        }
        .news-date {
            color: #7f8c8d;
            font-size: 0.85em;
            direction: ltr;
            text-align: left;
        }
        .no-news {
            text-align: center;
            padding: 60px 30px;
            color: #7f8c8d;
        }
        .no-news h2 {
            font-size: 2em;
            margin-bottom: 20px;
        }
        .filter-section {
            background: white;
            padding: 20px 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 20px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .filter-btn:hover, .filter-btn.active {
            background: #3498db;
            color: white;
        }
        @media (max-width: 768px) {
            .news-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            .nav {
                flex-direction: column;
                gap: 10px;
            }
            .filter-section {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇮🇶 الأخبار العراقية</h1>
            <div class="date">📅 ${new Date().toLocaleDateString('ar-EG', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            })}</div>
        </div>

        <div class="nav">
            <div>
                <a href="/">🏠 الرئيسية</a>
                <a href="/news">📰 الأخبار</a>
                <a href="/news/today">📅 أخبار اليوم</a>
                <a href="/api/news/stats">📊 الإحصائيات</a>
            </div>
            <div class="nav-right">
                <div class="stats">
                    📈 ${newsToShow.length} خبر متاح
                </div>
                <button class="clear-btn" id="clearNewsBtn" onclick="clearAllNews()">
                    🗑️ تنظيف الأخبار
                </button>
            </div>
        </div>

        <div class="filter-section">
            <strong>🔍 تصفية حسب:</strong>
            <a href="/news" class="filter-btn active">الكل</a>
            <a href="/news/category/politics" class="filter-btn">سياسة</a>
            <a href="/news/category/economy" class="filter-btn">اقتصاد</a>
            <a href="/news/category/security" class="filter-btn">أمن</a>
            <a href="/news/category/sports" class="filter-btn">رياضة</a>
            <a href="/news/category/health" class="filter-btn">صحة</a>
        </div>

        <div class="news-container">
            ${newsToShow.length > 0 ? `
                <div class="news-grid">
                    ${newsToShow.map(news => `
                        <a href="${news.url && news.url !== 'null' ? news.url : '#'}" target="_blank" class="news-card">
                            <div class="news-title">${news.title}</div>
                            <div class="news-meta">
                                <span class="news-source">${news.source}</span>
                                <span class="news-date">🕐 ${new Date(news.published_date).toLocaleString('ar-EG')}</span>
                            </div>
                        </a>
                    `).join('')}
                </div>
            ` : `
                <div class="no-news">
                    <h2>📭 لا توجد أخبار متاحة</h2>
                    <p>لم يتم العثور على أخبار لهذا اليوم. يرجى المحاولة لاحقاً.</p>
                    <br>
                    <a href="/api/news/admin/trigger" class="filter-btn">🔄 تحديث الأخبار</a>
                </div>
            `}
        </div>
    </div>

    <script>
        // Auto refresh every 5 minutes
        setTimeout(() => {
            window.location.reload();
        }, 300000);

        // Clear all news function
        async function clearAllNews() {
            console.log('تم النقر على زر تنظيف الأخبار');

            if (!confirm('هل أنت متأكد من حذف جميع الأخبار؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                console.log('تم إلغاء العملية من قبل المستخدم');
                return;
            }

            const clearBtn = document.getElementById('clearNewsBtn');
            if (!clearBtn) {
                console.error('لم يتم العثور على زر التنظيف');
                alert('خطأ: لم يتم العثور على زر التنظيف');
                return;
            }

            const originalText = clearBtn.textContent;
            console.log('بدء عملية الحذف...');

            // Update button state
            clearBtn.disabled = true;
            clearBtn.textContent = '⏳ جاري الحذف...';

            try {
                console.log('إرسال طلب الحذف...');
                const response = await fetch('/api/news/admin/clear', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('تم استلام الرد:', response.status);

                if (!response.ok) {
                    throw new Error(\`HTTP error! status: \${response.status}\`);
                }

                const result = await response.json();
                console.log('نتيجة العملية:', result);

                if (result.success) {
                    alert(\`تم حذف \${result.deletedCount} خبر بنجاح!\`);
                    console.log('تم الحذف بنجاح، إعادة تحميل الصفحة...');
                    // Reload page to show empty state
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء الحذف: ' + (result.message || result.error));
                }

            } catch (error) {
                console.error('خطأ في العملية:', error);
                alert('حدث خطأ في الاتصال: ' + error.message);
            } finally {
                // Reset button state
                clearBtn.disabled = false;
                clearBtn.textContent = originalText;
            }
        }
    </script>
</body>
</html>`;

    res.send(htmlContent);
  } catch (error) {
    console.error('خطأ في عرض الأخبار:', error);
    res.status(500).send('حدث خطأ في تحميل الأخبار');
  }
});

// Today's news page
app.get('/news/today', async (req, res) => {
  try {
    const { getDatabase } = require('./config/database');
    const db = getDatabase();
    const today = new Date().toISOString().split('T')[0];

    const todayNews = await new Promise((resolve, reject) => {
      db.all(`
        SELECT * FROM news
        WHERE DATE(published_date) = ?
        ORDER BY published_date DESC
      `, [today], (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });

    const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أخبار اليوم - ${today}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .nav-left {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        .clear-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }
        .clear-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        .clear-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .content {
            padding: 30px;
        }
        .news-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-right: 5px solid #e74c3c;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            display: block;
        }
        .news-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-right-color: #27ae60;
        }
        .news-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }
        .news-source {
            background: #27ae60;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.85em;
        }
        .news-date {
            color: #7f8c8d;
            font-size: 0.85em;
            direction: ltr;
            text-align: left;
        }
        .no-news {
            text-align: center;
            padding: 60px 30px;
            color: #7f8c8d;
        }
        .stats-box {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 أخبار اليوم</h1>
            <p>${new Date().toLocaleDateString('ar-EG', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            })}</p>
        </div>

        <div class="nav">
            <div class="nav-left">
                <a href="/">🏠 الرئيسية</a>
                <a href="/news">📰 جميع الأخبار</a>
                <a href="/news/today">📅 أخبار اليوم</a>
                <a href="/api/news/stats">📊 الإحصائيات</a>
            </div>
            <button class="clear-btn" id="clearNewsBtn" onclick="clearAllNews()">
                🗑️ تنظيف الأخبار
            </button>
        </div>

        <div class="content">
            <div class="stats-box">
                <h3>📊 إحصائيات اليوم</h3>
                <p>تم العثور على <strong>${todayNews.length}</strong> خبر لهذا اليوم</p>
            </div>

            ${todayNews.length > 0 ?
                todayNews.map(news => `
                    <a href="${news.url && news.url !== 'null' ? news.url : '#'}" target="_blank" class="news-item">
                        <div class="news-title">${news.title}</div>
                        <div class="news-meta">
                            <span class="news-source">${news.source}</span>
                            <span class="news-date">🕐 ${new Date(news.published_date).toLocaleString('ar-EG')}</span>
                        </div>
                    </a>
                `).join('')
                :
                `<div class="no-news">
                    <h2>📭 لا توجد أخبار لهذا اليوم</h2>
                    <p>لم يتم نشر أخبار جديدة اليوم. تحقق من الأخبار الحديثة.</p>
                    <br>
                    <a href="/news" class="news-url">📰 عرض جميع الأخبار</a>
                </div>`
            }
        </div>
    </div>

    <script>
        // Clear all news function
        async function clearAllNews() {
            console.log('تم النقر على زر تنظيف الأخبار');

            if (!confirm('هل أنت متأكد من حذف جميع الأخبار؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                console.log('تم إلغاء العملية من قبل المستخدم');
                return;
            }

            const clearBtn = document.getElementById('clearNewsBtn');
            if (!clearBtn) {
                console.error('لم يتم العثور على زر التنظيف');
                alert('خطأ: لم يتم العثور على زر التنظيف');
                return;
            }

            const originalText = clearBtn.textContent;
            console.log('بدء عملية الحذف...');

            // Update button state
            clearBtn.disabled = true;
            clearBtn.textContent = '⏳ جاري الحذف...';

            try {
                console.log('إرسال طلب الحذف...');
                const response = await fetch('/api/news/admin/clear', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('تم استلام الرد:', response.status);

                if (!response.ok) {
                    throw new Error(\`HTTP error! status: \${response.status}\`);
                }

                const result = await response.json();
                console.log('نتيجة العملية:', result);

                if (result.success) {
                    alert(\`تم حذف \${result.deletedCount} خبر بنجاح!\`);
                    console.log('تم الحذف بنجاح، إعادة تحميل الصفحة...');
                    // Reload page to show empty state
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء الحذف: ' + (result.message || result.error));
                }

            } catch (error) {
                console.error('خطأ في العملية:', error);
                alert('حدث خطأ في الاتصال: ' + error.message);
            } finally {
                // Reset button state
                clearBtn.disabled = false;
                clearBtn.textContent = originalText;
            }
        }
    </script>
</body>
</html>`;

    res.send(htmlContent);
  } catch (error) {
    console.error('خطأ في عرض أخبار اليوم:', error);
    res.status(500).send('حدث خطأ في تحميل أخبار اليوم');
  }
});

// Category news page
app.get('/news/category/:category', async (req, res) => {
  try {
    const { getDatabase } = require('./config/database');
    const db = getDatabase();
    const category = req.params.category;

    const categoryNews = await new Promise((resolve, reject) => {
      db.all(`
        SELECT * FROM news
        WHERE category = ?
        ORDER BY published_date DESC
        LIMIT 30
      `, [category], (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });

    const categoryNames = {
      'politics': 'السياسة',
      'economy': 'الاقتصاد',
      'security': 'الأمن',
      'sports': 'الرياضة',
      'health': 'الصحة',
      'culture': 'الثقافة',
      'technology': 'التكنولوجيا',
      'general': 'عام'
    };

    const categoryName = categoryNames[category] || category;

    const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أخبار ${categoryName}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .categories {
            background: #ecf0f1;
            padding: 20px 30px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .category-btn {
            padding: 8px 16px;
            background: white;
            color: #2c3e50;
            text-decoration: none;
            border-radius: 20px;
            transition: all 0.3s ease;
            border: 2px solid #bdc3c7;
        }
        .category-btn:hover, .category-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        .content {
            padding: 30px;
        }
        .news-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .news-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-right: 5px solid #9b59b6;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            display: block;
        }
        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-right-color: #e74c3c;
        }
        .news-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85em;
        }
        .news-source {
            background: #27ae60;
            color: white;
            padding: 6px 12px;
            border-radius: 12px;
            font-weight: bold;
        }
        .news-date {
            color: #7f8c8d;
            direction: ltr;
            text-align: left;
        }
        .stats-box {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .no-news {
            text-align: center;
            padding: 60px 30px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📂 أخبار ${categoryName}</h1>
            <p>آخر الأخبار في فئة ${categoryName}</p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 جميع الأخبار</a>
            <a href="/news/today">📅 أخبار اليوم</a>
            <a href="/api/news/stats">📊 الإحصائيات</a>
        </div>

        <div class="categories">
            <a href="/news/category/politics" class="category-btn ${category === 'politics' ? 'active' : ''}">🏛️ سياسة</a>
            <a href="/news/category/economy" class="category-btn ${category === 'economy' ? 'active' : ''}">💰 اقتصاد</a>
            <a href="/news/category/security" class="category-btn ${category === 'security' ? 'active' : ''}">🛡️ أمن</a>
            <a href="/news/category/sports" class="category-btn ${category === 'sports' ? 'active' : ''}">⚽ رياضة</a>
            <a href="/news/category/health" class="category-btn ${category === 'health' ? 'active' : ''}">🏥 صحة</a>
            <a href="/news/category/culture" class="category-btn ${category === 'culture' ? 'active' : ''}">🎭 ثقافة</a>
            <a href="/news/category/technology" class="category-btn ${category === 'technology' ? 'active' : ''}">💻 تكنولوجيا</a>
            <a href="/news/category/general" class="category-btn ${category === 'general' ? 'active' : ''}">📰 عام</a>
        </div>

        <div class="content">
            <div class="stats-box">
                <h3>📊 إحصائيات فئة ${categoryName}</h3>
                <p>تم العثور على <strong>${categoryNews.length}</strong> خبر في هذه الفئة</p>
            </div>

            ${categoryNews.length > 0 ? `
                <div class="news-grid">
                    ${categoryNews.map(news => `
                        <a href="${news.url && news.url !== 'null' ? news.url : '#'}" target="_blank" class="news-card">
                            <div class="news-title">${news.title}</div>
                            <div class="news-meta">
                                <span class="news-source">${news.source}</span>
                                <span class="news-date">🕐 ${new Date(news.published_date).toLocaleDateString('ar-EG')}</span>
                            </div>
                        </a>
                    `).join('')}
                </div>
            ` : `
                <div class="no-news">
                    <h2>📭 لا توجد أخبار في فئة ${categoryName}</h2>
                    <p>لم يتم العثور على أخبار في هذه الفئة حالياً.</p>
                    <br>
                    <a href="/news" class="news-url">📰 عرض جميع الأخبار</a>
                </div>
            `}
        </div>
    </div>
</body>
</html>`;

    res.send(htmlContent);
  } catch (error) {
    console.error('خطأ في عرض أخبار الفئة:', error);
    res.status(500).send('حدث خطأ في تحميل أخبار الفئة');
  }
});

// Admin system status page
app.get('/admin/system', async (req, res) => {
  try {
    const { getDatabase } = require('./config/database');
    const db = getDatabase();
    const NewsScheduler = require('./utils/scheduler');

    // Get database stats
    const newsCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM news', [], (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });

    const todayNewsCount = await new Promise((resolve, reject) => {
      const today = new Date().toISOString().split('T')[0];
      db.get('SELECT COUNT(*) as count FROM news WHERE DATE(published_date) = ?', [today], (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });

    const sourceStats = await new Promise((resolve, reject) => {
      db.all('SELECT source, COUNT(*) as count FROM news GROUP BY source ORDER BY count DESC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });

    const categoryStats = await new Promise((resolve, reject) => {
      db.all('SELECT category, COUNT(*) as count FROM news GROUP BY category ORDER BY count DESC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });

    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();

    const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة النظام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-right: 5px solid #9b59b6;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #9b59b6;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }
        .section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #9b59b6;
            padding-bottom: 10px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-right: 3px solid #3498db;
        }
        .chart-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        .chart-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }
        .chart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .chart-item:last-child {
            border-bottom: none;
        }
        .chart-bar {
            height: 20px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 10px;
            margin: 0 10px;
            min-width: 20px;
        }
        .refresh-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
        }
        .refresh-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ حالة النظام</h1>
            <p>لوحة تحكم شاملة لمراقبة النظام</p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 الأخبار</a>
            <a href="/health">💚 حالة الخادم</a>
            <a href="/admin/system">⚙️ حالة النظام</a>
            <a href="/admin/trigger">🔄 تحديث الأخبار</a>
        </div>

        <div class="content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">${newsCount}</div>
                    <div class="stat-label">إجمالي الأخبار</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${todayNewsCount}</div>
                    <div class="stat-label">أخبار اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${sourceStats.length}</div>
                    <div class="stat-label">المصادر النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${categoryStats.length}</div>
                    <div class="stat-label">الفئات المتاحة</div>
                </div>
            </div>

            <div class="section">
                <h3>🖥️ معلومات الخادم</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span>🕐 مدة التشغيل:</span>
                        <span>${Math.floor(uptime / 3600)}س ${Math.floor((uptime % 3600) / 60)}د</span>
                    </div>
                    <div class="info-item">
                        <span>💾 استخدام الذاكرة:</span>
                        <span>${(memoryUsage.heapUsed / 1024 / 1024).toFixed(1)} MB</span>
                    </div>
                    <div class="info-item">
                        <span>📊 إجمالي الذاكرة:</span>
                        <span>${(memoryUsage.heapTotal / 1024 / 1024).toFixed(1)} MB</span>
                    </div>
                    <div class="info-item">
                        <span>🖥️ إصدار Node.js:</span>
                        <span>${process.version}</span>
                    </div>
                    <div class="info-item">
                        <span>🏗️ المنصة:</span>
                        <span>${process.platform}</span>
                    </div>
                    <div class="info-item">
                        <span>⚡ معرف العملية:</span>
                        <span>${process.pid}</span>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-box">
                    <div class="chart-title">📊 إحصائيات المصادر</div>
                    ${sourceStats.map(source => {
                        const percentage = Math.round((source.count / newsCount) * 100);
                        return `
                            <div class="chart-item">
                                <span>${source.source}</span>
                                <div class="chart-bar" style="width: ${percentage * 2}px;"></div>
                                <span>${source.count} (${percentage}%)</span>
                            </div>
                        `;
                    }).join('')}
                </div>

                <div class="chart-box">
                    <div class="chart-title">📂 إحصائيات الفئات</div>
                    ${categoryStats.map(category => {
                        const percentage = Math.round((category.count / newsCount) * 100);
                        return `
                            <div class="chart-item">
                                <span>${category.category}</span>
                                <div class="chart-bar" style="width: ${percentage * 2}px;"></div>
                                <span>${category.count} (${percentage}%)</span>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>

            <button class="refresh-btn" onclick="window.location.reload()">🔄 تحديث البيانات</button>

            <div class="timestamp">
                آخر تحديث: ${new Date().toLocaleString('ar-EG')}
            </div>
        </div>
    </div>

    <script>
        // Auto refresh every 60 seconds
        setTimeout(() => {
            window.location.reload();
        }, 60000);
    </script>
</body>
</html>`;

    res.send(htmlContent);
  } catch (error) {
    console.error('خطأ في عرض حالة النظام:', error);
    res.status(500).send('حدث خطأ في تحميل حالة النظام');
  }
});

// Admin trigger page
app.get('/admin/trigger', (req, res) => {
  const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث الأخبار يدوياً</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .trigger-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        .trigger-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px 40px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        .trigger-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(231, 76, 60, 0.4);
        }
        .trigger-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .status-box {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-right: 5px solid #3498db;
        }
        .status-text {
            font-size: 1.1em;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }
        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            display: none;
        }
        .log-line {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-success {
            color: #2ecc71;
        }
        .log-error {
            color: #e74c3c;
        }
        .log-info {
            color: #3498db;
        }
        .warning-box {
            background: #f39c12;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-right: 5px solid #e74c3c;
        }
        .info-value {
            font-size: 2em;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        .info-label {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 تحديث الأخبار يدوياً</h1>
            <p>تشغيل عملية جمع الأخبار من جميع المصادر</p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 الأخبار</a>
            <a href="/health">💚 حالة الخادم</a>
            <a href="/admin/system">⚙️ حالة النظام</a>
            <a href="/admin/trigger">🔄 تحديث الأخبار</a>
        </div>

        <div class="content">
            <div class="warning-box">
                ⚠️ تنبيه: عملية التحديث قد تستغرق عدة دقائق حسب استجابة المصادر
            </div>

            <div class="info-grid">
                <div class="info-card">
                    <div class="info-value" id="currentNews">-</div>
                    <div class="info-label">الأخبار الحالية</div>
                </div>
                <div class="info-card">
                    <div class="info-value" id="lastUpdate">-</div>
                    <div class="info-label">آخر تحديث</div>
                </div>
                <div class="info-card">
                    <div class="info-value" id="activeSources">-</div>
                    <div class="info-label">المصادر النشطة</div>
                </div>
            </div>

            <div class="trigger-section">
                <h3>🚀 بدء عملية التحديث</h3>
                <p>اضغط على الزر أدناه لبدء جمع الأخبار من جميع المصادر المتاحة</p>

                <button class="trigger-btn" id="triggerBtn" onclick="startUpdate()">
                    🔄 بدء التحديث الآن
                </button>

                <div class="status-box" id="statusBox" style="display: none;">
                    <div class="status-text" id="statusText">جاري التحضير...</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>

            <div class="log-container" id="logContainer">
                <div class="log-line log-info">📋 سجل العمليات:</div>
            </div>
        </div>
    </div>

    <script>
        let updateInProgress = false;

        // Load initial stats
        loadStats();

        async function loadStats() {
            try {
                const response = await fetch('/api/news/stats');
                const stats = await response.json();

                document.getElementById('currentNews').textContent = stats.total || 0;
                document.getElementById('activeSources').textContent = Object.keys(stats.sources || {}).length;
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('ar-EG');
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        async function startUpdate() {
            if (updateInProgress) return;

            updateInProgress = true;
            const triggerBtn = document.getElementById('triggerBtn');
            const statusBox = document.getElementById('statusBox');
            const statusText = document.getElementById('statusText');
            const progressFill = document.getElementById('progressFill');
            const logContainer = document.getElementById('logContainer');

            // Update UI
            triggerBtn.disabled = true;
            triggerBtn.textContent = '⏳ جاري التحديث...';
            statusBox.style.display = 'block';
            logContainer.style.display = 'block';

            // Add log entry
            addLog('🚀 بدء عملية تحديث الأخبار...', 'info');

            try {
                // Start progress animation
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += 2;
                    if (progress <= 90) {
                        progressFill.style.width = progress + '%';
                        statusText.textContent = \`جاري جمع الأخبار... \${progress}%\`;
                    }
                }, 200);

                // Make API call
                const response = await fetch('/api/news/admin/trigger', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                // Complete progress
                clearInterval(progressInterval);
                progressFill.style.width = '100%';
                statusText.textContent = 'تم الانتهاء من التحديث!';

                // Add success log
                addLog(\`✅ تم حفظ \${result.saved || 0} خبر جديد\`, 'success');
                if (result.errors > 0) {
                    addLog(\`⚠️ حدث \${result.errors} خطأ أثناء العملية\`, 'error');
                }

                // Reload stats
                setTimeout(loadStats, 1000);

            } catch (error) {
                addLog(\`❌ خطأ في التحديث: \${error.message}\`, 'error');
                statusText.textContent = 'حدث خطأ أثناء التحديث';
            } finally {
                // Reset UI
                setTimeout(() => {
                    updateInProgress = false;
                    triggerBtn.disabled = false;
                    triggerBtn.textContent = '🔄 بدء التحديث الآن';
                    statusBox.style.display = 'none';
                    progressFill.style.width = '0%';
                }, 3000);
            }
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logLine = document.createElement('div');
            logLine.className = \`log-line log-\${type}\`;
            logLine.textContent = \`[\${new Date().toLocaleTimeString('ar-EG')}] \${message}\`;
            logContainer.appendChild(logLine);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Auto refresh stats every 30 seconds
        setInterval(loadStats, 30000);
    </script>
</body>
</html>`;

  res.send(htmlContent);
});

// Admin clear news page
app.get('/admin/clear', (req, res) => {
  const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف الأخبار</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .warning-box {
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            font-weight: bold;
        }
        .action-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        .action-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .action-section p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .clear-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        .clear-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(231, 76, 60, 0.4);
        }
        .clear-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .clear-btn.secondary {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        }
        .clear-btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(243, 156, 18, 0.4);
        }
        .status-box {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-right: 5px solid #3498db;
            display: none;
        }
        .status-text {
            font-size: 1.1em;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-right: 5px solid #e74c3c;
        }
        .info-value {
            font-size: 2em;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        .info-label {
            color: #666;
            font-size: 0.9em;
        }
        .days-input {
            padding: 10px 15px;
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            font-size: 1em;
            margin: 0 10px;
            width: 80px;
            text-align: center;
        }
        .days-input:focus {
            outline: none;
            border-color: #3498db;
        }
        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
            display: none;
        }
        .log-line {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-success {
            color: #2ecc71;
        }
        .log-error {
            color: #e74c3c;
        }
        .log-info {
            color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗑️ تنظيف الأخبار</h1>
            <p>إدارة وحذف الأخبار من قاعدة البيانات</p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 الأخبار</a>
            <a href="/health">💚 حالة الخادم</a>
            <a href="/admin/system">⚙️ حالة النظام</a>
            <a href="/admin/trigger">🔄 تحديث الأخبار</a>
            <a href="/admin/clear">🗑️ تنظيف الأخبار</a>
        </div>

        <div class="content">
            <div class="warning-box">
                ⚠️ تحذير: عمليات الحذف لا يمكن التراجع عنها. تأكد من اختيارك قبل المتابعة
            </div>

            <div class="info-grid">
                <div class="info-card">
                    <div class="info-value" id="currentNews">-</div>
                    <div class="info-label">إجمالي الأخبار</div>
                </div>
                <div class="info-card">
                    <div class="info-value" id="todayNews">-</div>
                    <div class="info-label">أخبار اليوم</div>
                </div>
                <div class="info-card">
                    <div class="info-value" id="oldNews">-</div>
                    <div class="info-label">أخبار قديمة (>7 أيام)</div>
                </div>
            </div>

            <div class="action-section">
                <h3>🗑️ حذف جميع الأخبار</h3>
                <p>سيتم حذف جميع الأخبار من قاعدة البيانات نهائياً</p>
                <button class="clear-btn" id="clearAllBtn" onclick="clearAllNews()">
                    🗑️ حذف جميع الأخبار
                </button>
            </div>

            <div class="action-section">
                <h3>📅 حذف الأخبار القديمة</h3>
                <p>حذف الأخبار الأقدم من عدد الأيام المحدد</p>
                <input type="number" class="days-input" id="daysInput" value="7" min="1" max="365">
                <button class="clear-btn secondary" id="clearOldBtn" onclick="clearOldNews()">
                    📅 حذف الأخبار القديمة
                </button>
            </div>

            <div class="status-box" id="statusBox">
                <div class="status-text" id="statusText">جاري المعالجة...</div>
            </div>

            <div class="log-container" id="logContainer">
                <div class="log-line log-info">📋 سجل العمليات:</div>
            </div>
        </div>
    </div>

    <script>
        // Load initial stats
        loadStats();

        async function loadStats() {
            try {
                const response = await fetch('/api/news/stats');
                const stats = await response.json();

                document.getElementById('currentNews').textContent = stats.total || 0;

                // Get today's news count
                const today = new Date().toISOString().split('T')[0];
                const todayResponse = await fetch(\`/api/news?date=\${today}\`);
                const todayData = await todayResponse.json();
                document.getElementById('todayNews').textContent = todayData.data?.length || 0;

                // Calculate old news (>7 days)
                const oldNewsCount = Math.max(0, (stats.total || 0) - (todayData.data?.length || 0));
                document.getElementById('oldNews').textContent = oldNewsCount;

            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        async function clearAllNews() {
            if (!confirm('هل أنت متأكد من حذف جميع الأخبار؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                return;
            }

            const clearAllBtn = document.getElementById('clearAllBtn');
            const statusBox = document.getElementById('statusBox');
            const statusText = document.getElementById('statusText');
            const logContainer = document.getElementById('logContainer');

            // Update UI
            clearAllBtn.disabled = true;
            clearAllBtn.textContent = '⏳ جاري الحذف...';
            statusBox.style.display = 'block';
            logContainer.style.display = 'block';
            statusText.textContent = 'جاري حذف جميع الأخبار...';

            // Add log entry
            addLog('🗑️ بدء عملية حذف جميع الأخبار...', 'info');

            try {
                const response = await fetch('/api/news/admin/clear', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    statusText.textContent = \`تم حذف \${result.deletedCount} خبر بنجاح!\`;
                    addLog(\`✅ تم حذف \${result.deletedCount} خبر من قاعدة البيانات\`, 'success');
                } else {
                    statusText.textContent = 'حدث خطأ أثناء الحذف';
                    addLog(\`❌ خطأ: \${result.message}\`, 'error');
                }

                // Reload stats
                setTimeout(loadStats, 1000);

            } catch (error) {
                addLog(\`❌ خطأ في الحذف: \${error.message}\`, 'error');
                statusText.textContent = 'حدث خطأ أثناء الحذف';
            } finally {
                // Reset UI
                setTimeout(() => {
                    clearAllBtn.disabled = false;
                    clearAllBtn.textContent = '🗑️ حذف جميع الأخبار';
                    statusBox.style.display = 'none';
                }, 3000);
            }
        }

        async function clearOldNews() {
            const days = parseInt(document.getElementById('daysInput').value) || 7;

            if (!confirm(\`هل أنت متأكد من حذف الأخبار الأقدم من \${days} أيام؟\`)) {
                return;
            }

            const clearOldBtn = document.getElementById('clearOldBtn');
            const statusBox = document.getElementById('statusBox');
            const statusText = document.getElementById('statusText');
            const logContainer = document.getElementById('logContainer');

            // Update UI
            clearOldBtn.disabled = true;
            clearOldBtn.textContent = '⏳ جاري الحذف...';
            statusBox.style.display = 'block';
            logContainer.style.display = 'block';
            statusText.textContent = \`جاري حذف الأخبار الأقدم من \${days} أيام...\`;

            // Add log entry
            addLog(\`📅 بدء عملية حذف الأخبار الأقدم من \${days} أيام...\`, 'info');

            try {
                const response = await fetch(\`/api/news/admin/clear/\${days}\`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    statusText.textContent = \`تم حذف \${result.deletedCount} خبر قديم بنجاح!\`;
                    addLog(\`✅ تم حذف \${result.deletedCount} خبر أقدم من \${days} أيام\`, 'success');
                } else {
                    statusText.textContent = 'حدث خطأ أثناء الحذف';
                    addLog(\`❌ خطأ: \${result.message}\`, 'error');
                }

                // Reload stats
                setTimeout(loadStats, 1000);

            } catch (error) {
                addLog(\`❌ خطأ في الحذف: \${error.message}\`, 'error');
                statusText.textContent = 'حدث خطأ أثناء الحذف';
            } finally {
                // Reset UI
                setTimeout(() => {
                    clearOldBtn.disabled = false;
                    clearOldBtn.textContent = '📅 حذف الأخبار القديمة';
                    statusBox.style.display = 'none';
                }, 3000);
            }
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logLine = document.createElement('div');
            logLine.className = \`log-line log-\${type}\`;
            logLine.textContent = \`[\${new Date().toLocaleTimeString('ar-EG')}] \${message}\`;
            logContainer.appendChild(logLine);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Auto refresh stats every 30 seconds
        setInterval(loadStats, 30000);
    </script>
</body>
</html>`;

  res.send(htmlContent);
});

// Test clear button page
app.get('/test-clear', (req, res) => {
  const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر التنظيف</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .clear-btn {
            background: #e74c3c;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .clear-btn:hover {
            background: #c0392b;
        }
        .clear-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }
        .log {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-line {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار زر تنظيف الأخبار</h1>

        <button id="clearBtn" class="clear-btn" onclick="testClearNews()">
            🗑️ اختبار تنظيف الأخبار
        </button>

        <button class="clear-btn" onclick="checkNews()">
            📊 فحص عدد الأخبار
        </button>

        <button class="clear-btn" onclick="clearLog()">
            🧹 مسح السجل
        </button>

        <div id="log" class="log">
            <div class="log-line">📋 سجل الاختبار:</div>
        </div>
    </div>

    <script>
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const logLine = document.createElement('div');
            logLine.className = 'log-line';
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            logLine.textContent = \`[\${timestamp}] \${message}\`;
            log.appendChild(logLine);
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            const log = document.getElementById('log');
            log.innerHTML = '<div class="log-line">📋 سجل الاختبار:</div>';
        }

        async function checkNews() {
            addLog('🔍 فحص عدد الأخبار...');
            try {
                const response = await fetch('/api/news/stats');
                const result = await response.json();
                addLog(\`📊 عدد الأخبار الحالي: \${result.data.total_news}\`);
            } catch (error) {
                addLog(\`❌ خطأ في فحص الأخبار: \${error.message}\`);
            }
        }

        async function testClearNews() {
            addLog('🚀 بدء اختبار تنظيف الأخبار...');

            const clearBtn = document.getElementById('clearBtn');
            const originalText = clearBtn.textContent;

            // تحديث حالة الزر
            clearBtn.disabled = true;
            clearBtn.textContent = '⏳ جاري الاختبار...';

            try {
                addLog('📡 إرسال طلب DELETE إلى /api/news/admin/clear');

                const response = await fetch('/api/news/admin/clear', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                addLog(\`📨 تم استلام الرد - الحالة: \${response.status}\`);

                if (!response.ok) {
                    throw new Error(\`HTTP error! status: \${response.status}\`);
                }

                const result = await response.json();
                addLog(\`📋 نتيجة العملية: \${JSON.stringify(result, null, 2)}\`);

                if (result.success) {
                    addLog(\`✅ نجح الحذف! تم حذف \${result.deletedCount} خبر\`);
                } else {
                    addLog(\`❌ فشل الحذف: \${result.message || result.error}\`);
                }

            } catch (error) {
                addLog(\`💥 خطأ في العملية: \${error.message}\`);
                addLog(\`🔍 تفاصيل الخطأ: \${error.stack}\`);
            } finally {
                // استعادة حالة الزر
                clearBtn.disabled = false;
                clearBtn.textContent = originalText;
                addLog('🔄 تم إنهاء الاختبار');
            }
        }

        // فحص الأخبار عند تحميل الصفحة
        window.onload = function() {
            addLog('🌟 تم تحميل صفحة الاختبار');
            checkNews();
        };
    </script>
</body>
</html>`;

  res.send(htmlContent);
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'حدث خطأ في الخادم',
    message: process.env.NODE_ENV === 'development' ? err.message : 'خطأ داخلي'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'الصفحة غير موجودة',
    message: 'المسار المطلوب غير متاح'
  });
});

// Initialize database and start server
async function startServer() {
  try {
    await initDatabase();
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    
    // Start the scheduler for news updates
    startScheduler();
    console.log('✅ تم تشغيل جدولة تحديث الأخبار');
    
    app.listen(PORT, () => {
      console.log(`🚀 خادم API الأخبار العراقية يعمل على المنفذ ${PORT}`);
      console.log(`🌐 الرابط: http://localhost:${PORT}`);
      console.log(`📊 لوحة التحكم: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    console.error('❌ خطأ في تشغيل الخادم:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 إيقاف الخادم...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 إيقاف الخادم...');
  process.exit(0);
});

startServer();
