const axios = require('axios');

// Helper functions for web scraping

// Get random user agent
function getRandomUserAgent() {
  const userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  ];
  
  return userAgents[Math.floor(Math.random() * userAgents.length)];
}

// Get enhanced headers for scraping
function getScrapingHeaders(referer = null) {
  return {
    'User-Agent': getRandomUserAgent(),
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
    'Accept-Encoding': 'gzip, deflate, br',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Cache-Control': 'max-age=0',
    ...(referer && { 'Referer': referer })
  };
}

// Enhanced axios request with retry logic
async function makeRequest(url, options = {}) {
  const maxRetries = 3;
  const retryDelay = 2000; // 2 seconds
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await axios.get(url, {
        timeout: 15000,
        headers: getScrapingHeaders(options.referer),
        ...options
      });
      
      return response;
    } catch (error) {
      console.log(`محاولة ${attempt}/${maxRetries} فشلت لـ ${url}: ${error.message}`);
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }
}

// Clean and normalize Arabic text
function cleanArabicText(text) {
  if (!text) return '';
  
  return text
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[\u200B-\u200D\uFEFF]/g, '') // Remove zero-width characters
    .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\u0020-\u007E\n]/g, ' ') // Keep only Arabic, English, and basic punctuation
    .trim();
}

// Extract date from Arabic text
function extractArabicDate(text) {
  if (!text) return null;
  
  // Common Arabic date patterns
  const patterns = [
    /(\d{1,2})\/(\d{1,2})\/(\d{4})/,
    /(\d{4})-(\d{1,2})-(\d{1,2})/,
    /(\d{1,2})-(\d{1,2})-(\d{4})/
  ];
  
  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      return match[0];
    }
  }
  
  return null;
}

// Detect category from Arabic text
function detectCategoryFromText(text) {
  if (!text) return 'general';
  
  const lowerText = text.toLowerCase();
  
  const categoryKeywords = {
    'politics': ['سياسة', 'سياسي', 'حكومة', 'برلمان', 'وزير', 'رئيس', 'انتخابات', 'حزب'],
    'economy': ['اقتصاد', 'اقتصادي', 'مالي', 'استثمار', 'بنك', 'تجارة', 'صناعة', 'نفط'],
    'security': ['أمن', 'أمني', 'شرطة', 'جيش', 'عسكري', 'إرهاب', 'جريمة', 'اعتقال'],
    'sports': ['رياضة', 'رياضي', 'كرة', 'فريق', 'مباراة', 'بطولة', 'لاعب', 'نادي'],
    'culture': ['ثقافة', 'ثقافي', 'فن', 'أدب', 'مسرح', 'سينما', 'موسيقى', 'تراث'],
    'technology': ['تكنولوجيا', 'تقنية', 'إنترنت', 'حاسوب', 'ذكي', 'رقمي', 'تطبيق'],
    'health': ['صحة', 'طب', 'طبي', 'مرض', 'علاج', 'مستشفى', 'دواء', 'وباء']
  };
  
  for (const [category, keywords] of Object.entries(categoryKeywords)) {
    for (const keyword of keywords) {
      if (lowerText.includes(keyword)) {
        return category;
      }
    }
  }
  
  return 'general';
}

// Validate URL
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Extract domain from URL
function extractDomain(url) {
  try {
    return new URL(url).hostname;
  } catch {
    return null;
  }
}

// Sleep function for delays
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Rate limiting helper
class RateLimiter {
  constructor(requestsPerMinute = 30) {
    this.requests = [];
    this.limit = requestsPerMinute;
  }
  
  async waitIfNeeded() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    // Remove old requests
    this.requests = this.requests.filter(time => time > oneMinuteAgo);
    
    if (this.requests.length >= this.limit) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = oldestRequest + 60000 - now;
      
      if (waitTime > 0) {
        console.log(`⏳ انتظار ${Math.ceil(waitTime / 1000)} ثانية للحد من السرعة...`);
        await sleep(waitTime);
      }
    }
    
    this.requests.push(now);
  }
}

// Create a global rate limiter
const globalRateLimiter = new RateLimiter(20); // 20 requests per minute

module.exports = {
  getRandomUserAgent,
  getScrapingHeaders,
  makeRequest,
  cleanArabicText,
  extractArabicDate,
  detectCategoryFromText,
  isValidUrl,
  extractDomain,
  sleep,
  RateLimiter,
  globalRateLimiter
};
