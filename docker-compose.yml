version: '3.8'

services:
  iraqi-news-api:
    build: .
    container_name: iraqi-news-api
    restart: unless-stopped
    ports:
      - "5020:5020"
    environment:
      - NODE_ENV=production
      - PORT=5020
      - DB_PATH=/app/data/iraqi_news.db
      - SCRAPE_INTERVAL_MINUTES=30
      - CLEANUP_INTERVAL_HOURS=2
      - MAX_ARTICLES_PER_SCRAPE=20
      - ARTICLE_RETENTION_DAYS=30
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - TIMEZONE=Asia/Baghdad
      - LOG_LEVEL=info
      - CORS_ORIGIN=*
      - HELMET_ENABLED=true
      - SCRAPER_TIMEOUT_MS=15000
      - SHAFAQ_ENABLED=true
      - HATHALYOUM_ENABLED=true
      - MEDIA964_ENABLED=true
      - ALSUMARIA_ENABLED=true
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:5020/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - iraqi-news-network

  # Optional: Add nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: iraqi-news-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - iraqi-news-api
    networks:
      - iraqi-news-network

networks:
  iraqi-news-network:
    driver: bridge

volumes:
  data:
    driver: local
  logs:
    driver: local
