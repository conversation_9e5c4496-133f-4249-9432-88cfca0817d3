#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جامع الأخبار العراقية - Iraqi News Scraper
"""

import requests
import feedparser
from bs4 import BeautifulSoup
import logging
from datetime import datetime, timedelta
import pytz
from typing import List, Dict
import re
import time

logger = logging.getLogger(__name__)

class NewsScraper:
    def __init__(self, database):
        """تهيئة جامع الأخبار"""
        self.db = database
        self.iraq_tz = pytz.timezone('Asia/Baghdad')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # كلمات مفتاحية للأخبار العراقية
        self.iraqi_keywords = [
            'العراق', 'بغداد', 'البصرة', 'أربيل', 'الموصل', 'النجف', 'كربلاء', 'الأنبار',
            'ديالى', 'صلاح الدين', 'كركوك', 'واسط', 'ميسان', 'ذي قار', 'المثنى',
            'بابل', 'القادسية', 'دهوك', 'السليمانية', 'حلبجة', 'عراقي', 'عراقية',
            'الحكومة العراقية', 'البرلمان العراقي', 'رئيس الوزراء العراقي',
            'وزارة', 'محافظة', 'محافظ', 'نائب', 'وزير', 'مجلس النواب',
            'الكاظمي', 'السوداني', 'صالح', 'الحلبوسي', 'العبادي', 'المالكي',
            'الصدر', 'الحكيم', 'العامري', 'البارزاني', 'طالباني',
            'الحشد الشعبي', 'القوات الأمنية', 'الجيش العراقي', 'الشرطة العراقية',
            'دينار عراقي', 'البنك المركزي العراقي', 'النفط العراقي',
            'الكهرباء', 'الماء', 'الخدمات', 'الاستثمار', 'التجارة',
            'الأمن', 'الإرهاب', 'داعش', 'القاعدة', 'العمليات الأمنية'
        ]
        
        # كلمات مستبعدة (الفن، الثقافة، الطقس، الرياضة)
        self.excluded_keywords = [
            'فن', 'فنان', 'فنانة', 'مسرح', 'سينما', 'فيلم', 'مسلسل', 'أغنية', 'موسيقى',
            'ثقافة', 'ثقافي', 'مهرجان', 'معرض', 'كتاب', 'شاعر', 'أدب', 'أديب',
            'طقس', 'أمطار', 'حرارة', 'رياح', 'عاصفة', 'ثلوج', 'مناخ',
            'رياضة', 'كرة', 'لاعب', 'مباراة', 'بطولة', 'دوري', 'منتخب', 'نادي',
            'أولمبياد', 'كأس', 'هدف', 'تسجيل', 'فوز', 'خسارة', 'تعادل'
        ]
    
    def is_iraqi_news(self, title: str, content: str = '') -> bool:
        """فحص ما إذا كان الخبر عراقي"""
        text = f"{title} {content}".lower()
        
        # فحص الكلمات المستبعدة أولاً
        for keyword in self.excluded_keywords:
            if keyword in text:
                return False
        
        # فحص الكلمات العراقية
        for keyword in self.iraqi_keywords:
            if keyword in text:
                return True
        
        return False
    
    def categorize_news(self, title: str, content: str = '') -> str:
        """تصنيف الأخبار"""
        text = f"{title} {content}".lower()
        
        political_keywords = ['سياسة', 'حكومة', 'برلمان', 'وزير', 'رئيس', 'انتخابات', 'حزب']
        economic_keywords = ['اقتصاد', 'تجارة', 'استثمار', 'بنك', 'نفط', 'غاز', 'دولار', 'دينار']
        security_keywords = ['أمن', 'شرطة', 'جيش', 'إرهاب', 'عملية', 'اعتقال', 'قتل', 'انفجار']
        health_keywords = ['صحة', 'مستشفى', 'طبيب', 'مرض', 'علاج', 'دواء', 'وباء']
        
        for keyword in political_keywords:
            if keyword in text:
                return 'سياسة'
        
        for keyword in economic_keywords:
            if keyword in text:
                return 'اقتصاد'
        
        for keyword in security_keywords:
            if keyword in text:
                return 'أمن'
        
        for keyword in health_keywords:
            if keyword in text:
                return 'صحة'
        
        return 'عام'
    
    def scrape_rss_feed(self, url: str, source_name: str) -> List[Dict]:
        """جمع الأخبار من RSS feed"""
        news_list = []
        try:
            logger.info(f"🔄 جمع الأخبار من {source_name}...")
            
            feed = feedparser.parse(url)
            
            for entry in feed.entries:
                try:
                    title = entry.get('title', '').strip()
                    content = entry.get('summary', entry.get('description', '')).strip()
                    link = entry.get('link', '')
                    
                    # فحص إذا كان الخبر عراقي
                    if not self.is_iraqi_news(title, content):
                        continue
                    
                    # تحويل التاريخ
                    published_date = datetime.now(self.iraq_tz)
                    if hasattr(entry, 'published_parsed') and entry.published_parsed:
                        published_date = datetime(*entry.published_parsed[:6])
                        published_date = self.iraq_tz.localize(published_date)
                    
                    # قبول الأخبار من آخر 24 ساعة بدلاً من اليوم فقط
                    now = datetime.now(self.iraq_tz)
                    time_diff = now - published_date
                    if time_diff.days > 1:
                        continue
                    
                    news_item = {
                        'title': title,
                        'content': content,
                        'summary': content[:200] + '...' if len(content) > 200 else content,
                        'url': link,
                        'source': source_name,
                        'category': self.categorize_news(title, content),
                        'published_date': published_date,
                        'is_iraqi': True,
                        'keywords': ', '.join([kw for kw in self.iraqi_keywords if kw in f"{title} {content}".lower()])
                    }
                    
                    news_list.append(news_item)
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة خبر من {source_name}: {str(e)}")
                    continue
            
            logger.info(f"📰 {source_name}: تم العثور على {len(news_list)} خبر عراقي")
            
        except Exception as e:
            logger.error(f"❌ خطأ في جمع الأخبار من {source_name}: {str(e)}")
        
        return news_list
    
    def scrape_website(self, url: str, source_name: str, selectors: Dict) -> List[Dict]:
        """جمع الأخبار من موقع ويب"""
        news_list = []
        try:
            logger.info(f"🔄 جمع الأخبار من {source_name}...")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن عناصر الأخبار
            news_elements = soup.select(selectors.get('container', '.news-item'))
            
            for element in news_elements[:20]:  # أخذ أول 20 خبر
                try:
                    title_elem = element.select_one(selectors.get('title', 'h2, h3, .title'))
                    title = title_elem.get_text().strip() if title_elem else ''
                    
                    content_elem = element.select_one(selectors.get('content', 'p, .content, .summary'))
                    content = content_elem.get_text().strip() if content_elem else ''
                    
                    link_elem = element.select_one(selectors.get('link', 'a'))
                    link = link_elem.get('href', '') if link_elem else ''
                    
                    if not title:
                        continue
                    
                    # فحص إذا كان الخبر عراقي
                    if not self.is_iraqi_news(title, content):
                        continue
                    
                    # إصلاح الرابط إذا كان نسبي
                    if link and not link.startswith('http'):
                        from urllib.parse import urljoin
                        link = urljoin(url, link)
                    
                    news_item = {
                        'title': title,
                        'content': content,
                        'summary': content[:200] + '...' if len(content) > 200 else content,
                        'url': link,
                        'source': source_name,
                        'category': self.categorize_news(title, content),
                        'published_date': datetime.now(self.iraq_tz),
                        'is_iraqi': True,
                        'keywords': ', '.join([kw for kw in self.iraqi_keywords if kw in f"{title} {content}".lower()])
                    }
                    
                    news_list.append(news_item)
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة خبر من {source_name}: {str(e)}")
                    continue
            
            logger.info(f"📰 {source_name}: تم العثور على {len(news_list)} خبر عراقي")
            
        except Exception as e:
            logger.error(f"❌ خطأ في جمع الأخبار من {source_name}: {str(e)}")
        
        return news_list
    


    def scrape_all_sources(self, status_callback=None) -> Dict:
        """جمع الأخبار من جميع المصادر"""
        total_saved = 0
        errors = []

        # جلب المصادر النشطة من قاعدة البيانات
        iraqi_sources = self.db.get_active_sources()

        if not iraqi_sources:
            logger.warning("⚠️ لا توجد مصادر نشطة في قاعدة البيانات")
            return {
                'total_saved': 0,
                'errors': ['لا توجد مصادر نشطة'],
                'timestamp': datetime.now(self.iraq_tz).isoformat()
            }

        logger.info(f"🔄 بدء جمع الأخبار من {len(iraqi_sources)} مصدر نشط...")

        # تحويل المصادر إلى التنسيق المطلوب
        sources_to_process = []
        for source in iraqi_sources:
            source_data = {
                'id': source['id'],
                'name': source['name'],
                'url': source['url'],
                'type': source['type']
            }

            # إضافة selectors إذا كانت متوفرة
            if source.get('selectors'):
                try:
                    import json
                    source_data['selectors'] = json.loads(source['selectors'])
                except:
                    # استخدام selectors افتراضية
                    source_data['selectors'] = {
                        'container': 'article, .news-item, .post, .entry',
                        'title': 'h1, h2, h3, .title, .headline',
                        'content': 'p, .content, .summary, .excerpt',
                        'link': 'a'
                    }
            else:
                source_data['selectors'] = {
                    'container': 'article, .news-item, .post, .entry',
                    'title': 'h1, h2, h3, .title, .headline',
                    'content': 'p, .content, .summary, .excerpt',
                    'link': 'a'
                }

            sources_to_process.append(source_data)

        # معالجة كل مصدر
        for source in sources_to_process:
            try:
                logger.info(f"🔄 معالجة المصدر: {source['name']}")

                # تحديث حالة المصدر في قاعدة البيانات
                self.db.update_source_status(source['id'], 'processing')

                if source['type'] == 'rss':
                    news_list = self.scrape_rss_feed(source['url'], source['name'])
                else:
                    news_list = self.scrape_website(source['url'], source['name'], source['selectors'])

                saved_count = 0
                for news in news_list:
                    if self.db.save_news(news):
                        saved_count += 1

                total_saved += saved_count

                # تحديث إحصائيات المصدر
                if saved_count > 0:
                    self.db.update_source_news_count(source['name'], saved_count)
                    self.db.update_source_status(source['id'], 'success')
                    logger.info(f"✅ {source['name']}: تم حفظ {saved_count} خبر")
                else:
                    self.db.update_source_status(source['id'], 'no_news', 'لم يتم العثور على أخبار جديدة')
                    logger.info(f"📭 {source['name']}: لا توجد أخبار جديدة")

            except Exception as e:
                error_msg = f"خطأ في {source['name']}: {str(e)}"
                errors.append(error_msg)
                self.db.update_source_status(source['id'], 'error', str(e))
                logger.error(f"❌ {error_msg}")

            time.sleep(1)  # توقف قصير بين المصادر

        # لا نضيف أخبار افتراضية - فقط الأخبار الحقيقية من المصادر

        logger.info(f"🎉 انتهت عملية جمع الأخبار: {total_saved} خبر محفوظ، {len(errors)} خطأ")

        return {
            'total_saved': total_saved,
            'errors': errors,
            'timestamp': datetime.now(self.iraq_tz).isoformat()
        }
