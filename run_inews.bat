@echo off
chcp 65001 >nul
title نظام أخبار العراق - iNews

echo.
echo ========================================
echo 🇮🇶 نظام أخبار العراق - iNews
echo ========================================
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

:: التحقق من وجود الملفات المطلوبة
if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo 🔍 فحص المتطلبات...

:: تثبيت المتطلبات إذا لم تكن مثبتة
python -c "import flask, requests, beautifulsoup4, feedparser" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المتطلبات بنجاح
) else (
    echo ✅ جميع المتطلبات مثبتة
)

echo.
echo 🗄️ فحص قاعدة البيانات...
if exist "iraqi_news.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo 🔧 إنشاء قاعدة البيانات...
)

echo.
echo 🚀 بدء تشغيل نظام أخبار العراق...
echo 📍 الخادم سيعمل على: http://localhost:5020
echo.

:: انتظار قصير ثم فتح المتصفح
echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak >nul
start http://localhost:5020

echo.
echo ==================================================
echo 💡 نصائح:
echo    • المتصفح سيفتح تلقائياً خلال ثوانٍ
echo    • اضغط Ctrl+C لإيقاف الخادم
echo    • النظام يجمع الأخبار تلقائياً كل دقيقة
echo    • يمكنك إغلاق هذه النافذة بعد فتح المتصفح
echo ==================================================
echo.

:: تشغيل التطبيق
python app.py

:: في حالة إغلاق التطبيق
echo.
echo 🛑 تم إيقاف نظام أخبار العراق
pause
