#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام أخبار العراق - Iraqi News System
تطبيق Flask لجمع وعرض أخبار العراق فقط
"""

from flask import Flask, jsonify, render_template_string, request, redirect, url_for
from flask_cors import CORS
import sqlite3
import threading
import schedule
import time
from datetime import datetime, timedelta
import pytz
import logging
from news_scraper import NewsScraper
from database import Database

# إعداد التطبيق
app = Flask(__name__)
CORS(app)

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('iraqi_news.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعداد قاعدة البيانات
db = Database()
scraper = NewsScraper(db)

# المنطقة الزمنية العراقية
IRAQ_TZ = pytz.timezone('Asia/Baghdad')

def run_scheduler():
    """تشغيل جدولة المهام في خيط منفصل"""
    while True:
        schedule.run_pending()
        time.sleep(60)

def collect_news():
    """جمع الأخبار من جميع المصادر"""
    try:
        logger.info("🚀 بدء عملية جمع الأخبار...")
        result = scraper.scrape_all_sources()
        logger.info(f"✅ تم جمع {result['total_saved']} خبر جديد")
        return result
    except Exception as e:
        logger.error(f"❌ خطأ في جمع الأخبار: {str(e)}")
        return {"total_saved": 0, "errors": [str(e)]}

# جدولة جمع الأخبار كل 30 دقيقة
schedule.every(30).minutes.do(collect_news)

# بدء جدولة المهام
scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
scheduler_thread.start()

@app.route('/')
def home():
    """الصفحة الرئيسية"""
    return render_template_string("""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇮🇶 نظام أخبار العراق</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.3em;
            opacity: 0.9;
        }
        .nav {
            background: #34495e;
            padding: 20px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
            display: inline-block;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            border-right: 5px solid #e74c3c;
            transition: transform 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-5px);
        }
        .feature h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        .feature p {
            color: #666;
            line-height: 1.6;
        }
        .stats {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
        }
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇮🇶 نظام أخبار العراق</h1>
            <p>مصدرك الموثوق للأخبار العراقية المحدثة</p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 الأخبار</a>
            <a href="/api/news">📡 API</a>
            <a href="/admin">⚙️ الإدارة</a>
        </div>

        <div class="content">
            <div class="features">
                <div class="feature">
                    <h3>📰 أخبار العراق فقط</h3>
                    <p>نركز حصرياً على الأخبار العراقية المهمة من مصادر موثوقة</p>
                </div>
                <div class="feature">
                    <h3>🕐 تحديث مستمر</h3>
                    <p>جمع تلقائي للأخبار كل 30 دقيقة من مصادر متعددة</p>
                </div>
                <div class="feature">
                    <h3>🎯 تصفية ذكية</h3>
                    <p>استبعاد أخبار الفن والثقافة والطقس والرياضة</p>
                </div>
                <div class="feature">
                    <h3>📅 أخبار اليوم</h3>
                    <p>عرض أخبار اليوم الحالي فقط مع التاريخ العراقي</p>
                </div>
            </div>

            <div class="stats">
                <h3>📊 إحصائيات سريعة</h3>
                <p>نظام متطور لجمع وعرض الأخبار العراقية بتقنية Python و Flask</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 نظام أخبار العراق - تم التطوير بواسطة Python & Flask</p>
        </div>
    </div>
</body>
</html>
    """)

@app.route('/api/news')
def api_news():
    """API لجلب الأخبار"""
    try:
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        page = request.args.get('page', 1, type=int)
        category = request.args.get('category', '')
        search = request.args.get('search', '')

        # إذا تم تمرير page، احسب offset
        if page > 1:
            offset = (page - 1) * limit

        if search:
            news = db.search_news(search, limit * 10)  # جلب أكثر للبحث
            total_count = len(news)
            news = news[offset:offset + limit]
        elif category:
            news = db.get_news_by_category(category, limit * 10)
            total_count = len(news)
            news = news[offset:offset + limit]
        else:
            all_news = db.get_all_news(limit * 10, 0)
            total_count = len(all_news)
            news = all_news[offset:offset + limit]

        total_pages = (total_count + limit - 1) // limit

        return jsonify({
            'success': True,
            'data': news,
            'count': len(news),
            'total_count': total_count,
            'page': page,
            'total_pages': total_pages,
            'per_page': limit
        })
    except Exception as e:
        logger.error(f"خطأ في API الأخبار: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/today')
def api_today_news():
    """API لجلب أخبار اليوم"""
    try:
        limit = request.args.get('limit', 50, type=int)
        news = db.get_today_news(limit)

        return jsonify({
            'success': True,
            'data': news,
            'count': len(news)
        })
    except Exception as e:
        logger.error(f"خطأ في API أخبار اليوم: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/stats')
def api_stats():
    """API لجلب إحصائيات الأخبار"""
    try:
        stats = db.get_stats()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"خطأ في API الإحصائيات: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/collect', methods=['POST'])
def api_collect_news():
    """API لجمع الأخبار يدوياً"""
    try:
        result = collect_news()
        return jsonify({
            'success': True,
            'message': 'تم جمع الأخبار بنجاح',
            'data': result
        })
    except Exception as e:
        logger.error(f"خطأ في جمع الأخبار: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/clear', methods=['POST'])
def api_clear_news():
    """API لحذف جميع الأخبار"""
    try:
        count = db.clear_all_news()
        return jsonify({
            'success': True,
            'message': f'تم حذف {count} خبر',
            'deleted_count': count
        })
    except Exception as e:
        logger.error(f"خطأ في حذف الأخبار: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/news')
def news_page():
    """صفحة عرض الأخبار"""
    try:
        # الحصول على رقم الصفحة من المعاملات
        page = request.args.get('page', 1, type=int)
        per_page = 20  # عدد الأخبار في كل صفحة
        offset = (page - 1) * per_page

        # جلب أخبار اليوم أولاً، ثم جميع الأخبار
        today_news = db.get_today_news(1000)  # جلب جميع أخبار اليوم
        if today_news:
            total_news = today_news
        else:
            total_news = db.get_all_news(1000)  # جلب جميع الأخبار

        # حساب إجمالي الصفحات
        total_count = len(total_news)
        total_pages = (total_count + per_page - 1) // per_page

        # جلب أخبار الصفحة الحالية
        start_idx = offset
        end_idx = start_idx + per_page
        news = total_news[start_idx:end_idx]

        return render_template_string("""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📰 أخبار العراق اليوم</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .clear-btn {
            background: #e74c3c;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .clear-btn:hover {
            background: #c0392b;
            transform: scale(1.05);
        }
        .content {
            padding: 30px;
        }
        .news-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-right: 5px solid #e74c3c;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .news-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .news-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .news-source {
            background: #27ae60;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: bold;
        }
        .news-category {
            background: #f39c12;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
        }
        .news-date {
            color: #7f8c8d;
        }
        .no-news {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 10px;
        }
        .pagination a, .pagination span {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        .pagination a:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        .pagination .current {
            background: #e74c3c;
            color: white;
            border-color: #e74c3c;
        }
        .pagination .disabled {
            color: #ccc;
            cursor: not-allowed;
        }
        .news-stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📰 أخبار العراق اليوم</h1>
            <p>آخر الأخبار العراقية المحدثة</p>
        </div>

        <div class="nav">
            <div>
                <a href="/">🏠 الرئيسية</a>
                <a href="/news">📰 الأخبار</a>
                <a href="/admin">⚙️ الإدارة</a>
            </div>
            <button class="clear-btn" onclick="clearNews()">🗑️ تنظيف الأخبار</button>
        </div>

        <div class="content">
            <!-- إحصائيات الأخبار -->
            <div class="news-stats">
                <strong>📊 إجمالي الأخبار: {{ total_count }}</strong> |
                <strong>📄 الصفحة: {{ page }} من {{ total_pages }}</strong> |
                <strong>📰 عرض: {{ news|length }} خبر</strong>
            </div>

            {% if news %}
                {% for item in news %}
                <div class="news-item" onclick="openNews('{{ item.url }}')">
                    <div class="news-title">{{ item.title }}</div>
                    <div class="news-meta">
                        <span class="news-source">{{ item.source }}</span>
                        <span class="news-category">{{ item.category }}</span>
                        <span class="news-date">🕐 {{ item.published_date }}</span>
                    </div>
                </div>
                {% endfor %}

                <!-- نظام الصفحات -->
                {% if total_pages > 1 %}
                <div class="pagination">
                    <!-- الصفحة السابقة -->
                    {% if page > 1 %}
                        <a href="/news?page={{ page - 1 }}">« السابق</a>
                    {% else %}
                        <span class="disabled">« السابق</span>
                    {% endif %}

                    <!-- أرقام الصفحات -->
                    {% for p in range(1, total_pages + 1) %}
                        {% if p == page %}
                            <span class="current">{{ p }}</span>
                        {% elif p <= 3 or p > total_pages - 3 or (p >= page - 2 and p <= page + 2) %}
                            <a href="/news?page={{ p }}">{{ p }}</a>
                        {% elif p == 4 and page > 6 %}
                            <span>...</span>
                        {% elif p == total_pages - 3 and page < total_pages - 5 %}
                            <span>...</span>
                        {% endif %}
                    {% endfor %}

                    <!-- الصفحة التالية -->
                    {% if page < total_pages %}
                        <a href="/news?page={{ page + 1 }}">التالي »</a>
                    {% else %}
                        <span class="disabled">التالي »</span>
                    {% endif %}
                </div>
                {% endif %}
            {% else %}
                <div class="no-news">
                    <h2>📭 لا توجد أخبار متاحة</h2>
                    <p>لم يتم العثور على أخبار عراقية اليوم</p>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        function openNews(url) {
            if (url && url !== 'None') {
                window.open(url, '_blank');
            }
        }

        async function clearNews() {
            if (!confirm('هل أنت متأكد من حذف جميع الأخبار؟')) return;

            try {
                const response = await fetch('/api/news/clear', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(`تم حذف ${result.deleted_count} خبر بنجاح!`);
                    window.location.reload();
                } else {
                    alert('حدث خطأ: ' + result.error);
                }
            } catch (error) {
                alert('حدث خطأ في الاتصال: ' + error.message);
            }
        }
    </script>
</body>
</html>
        """, news=news, page=page, total_pages=total_pages, total_count=total_count)
    except Exception as e:
        logger.error(f"خطأ في صفحة الأخبار: {str(e)}")
        return f"خطأ: {str(e)}", 500

@app.route('/admin')
def admin_page():
    """صفحة الإدارة"""
    try:
        stats = db.get_stats()

        return render_template_string("""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ إدارة نظام الأخبار</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-right: 5px solid #3498db;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .action-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }
        .action-btn {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .action-btn:hover {
            transform: scale(1.05);
        }
        .action-btn.danger {
            background: #e74c3c;
        }
        .action-btn.success {
            background: #27ae60;
        }
        .log {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: monospace;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ إدارة نظام الأخبار</h1>
            <p>لوحة التحكم والإدارة</p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 الأخبار</a>
            <a href="/admin">⚙️ الإدارة</a>
            <a href="/api/news">📡 API</a>
        </div>

        <div class="content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ stats.total_news }}</div>
                    <div class="stat-label">إجمالي الأخبار</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.today_news }}</div>
                    <div class="stat-label">أخبار اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.sources|length }}</div>
                    <div class="stat-label">المصادر النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.categories|length }}</div>
                    <div class="stat-label">الفئات المتاحة</div>
                </div>
            </div>

            <div class="actions">
                <div class="action-card">
                    <h3>🔄 جمع الأخبار</h3>
                    <p>تشغيل عملية جمع الأخبار يدوياً</p>
                    <button class="action-btn success" onclick="collectNews()">
                        🔄 جمع الأخبار الآن
                    </button>
                </div>

                <div class="action-card">
                    <h3>🗑️ تنظيف الأخبار</h3>
                    <p>حذف جميع الأخبار من قاعدة البيانات</p>
                    <button class="action-btn danger" onclick="clearNews()">
                        🗑️ حذف جميع الأخبار
                    </button>
                </div>
            </div>

            <div id="log" class="log">
                <div>📋 سجل العمليات:</div>
            </div>
        </div>
    </div>

    <script>
        function addLog(message) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            log.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        async function collectNews() {
            addLog('🔄 بدء عملية جمع الأخبار...');

            try {
                const response = await fetch('/api/news/collect', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLog(`✅ تم جمع ${result.data.total_saved} خبر جديد`);
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    addLog(`❌ خطأ: ${result.error}`);
                }
            } catch (error) {
                addLog(`💥 خطأ في الاتصال: ${error.message}`);
            }
        }

        async function clearNews() {
            if (!confirm('هل أنت متأكد من حذف جميع الأخبار؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                return;
            }

            addLog('🗑️ بدء عملية حذف الأخبار...');

            try {
                const response = await fetch('/api/news/clear', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLog(`✅ تم حذف ${result.deleted_count} خبر`);
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    addLog(`❌ خطأ: ${result.error}`);
                }
            } catch (error) {
                addLog(`💥 خطأ في الاتصال: ${error.message}`);
            }
        }

        // تحديث الصفحة كل دقيقة
        setInterval(() => {
            window.location.reload();
        }, 60000);
    </script>
</body>
</html>
        """, stats=stats)
    except Exception as e:
        logger.error(f"خطأ في صفحة الإدارة: {str(e)}")
        return f"خطأ: {str(e)}", 500

if __name__ == '__main__':
    # تشغيل جمع الأخبار عند بدء التطبيق
    collect_news()
    
    # تشغيل التطبيق
    app.run(host='0.0.0.0', port=5020, debug=True)
