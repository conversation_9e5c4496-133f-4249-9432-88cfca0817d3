const express = require('express');
const router = express.Router();
const News = require('../models/News');

// GET /api/news - Get all news with pagination and filters
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const source = req.query.source;
    const category = req.query.category;

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return res.status(400).json({
        error: 'معاملات غير صحيحة',
        message: 'رقم الصفحة يجب أن يكون أكبر من 0 وحد العناصر بين 1-100'
      });
    }

    const result = await News.getAll(page, limit, source, category);
    
    res.json({
      success: true,
      data: result.news,
      pagination: result.pagination,
      filters: {
        source: source || null,
        category: category || null
      }
    });
  } catch (error) {
    console.error('خطأ في جلب الأخبار:', error);
    res.status(500).json({
      error: 'خطأ في جلب الأخبار',
      message: error.message
    });
  }
});

// GET /api/news/latest - Get latest news
router.get('/latest', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    
    if (limit < 1 || limit > 50) {
      return res.status(400).json({
        error: 'معامل غير صحيح',
        message: 'حد العناصر يجب أن يكون بين 1-50'
      });
    }

    const news = await News.getLatest(limit);
    
    res.json({
      success: true,
      data: news,
      count: news.length
    });
  } catch (error) {
    console.error('خطأ في جلب أحدث الأخبار:', error);
    res.status(500).json({
      error: 'خطأ في جلب أحدث الأخبار',
      message: error.message
    });
  }
});

// GET /api/news/source/:source - Get news by source
router.get('/source/:source', async (req, res) => {
  try {
    const source = req.params.source;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    // Validate source
    const validSources = ['shafaq', 'hathalyoum', 'media964', 'alsumaria'];
    if (!validSources.includes(source)) {
      return res.status(400).json({
        error: 'مصدر غير صحيح',
        message: 'المصادر المتاحة: ' + validSources.join(', ')
      });
    }

    const result = await News.getBySource(source, page, limit);
    
    res.json({
      success: true,
      source: source,
      data: result.news,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('خطأ في جلب أخبار المصدر:', error);
    res.status(500).json({
      error: 'خطأ في جلب أخبار المصدر',
      message: error.message
    });
  }
});

// GET /api/news/category/:category - Get news by category
router.get('/category/:category', async (req, res) => {
  try {
    const category = req.params.category;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    // Validate category
    const validCategories = ['politics', 'economy', 'security', 'sports', 'culture', 'technology', 'health', 'general'];
    if (!validCategories.includes(category)) {
      return res.status(400).json({
        error: 'فئة غير صحيحة',
        message: 'الفئات المتاحة: ' + validCategories.join(', ')
      });
    }

    const result = await News.getByCategory(category, page, limit);
    
    res.json({
      success: true,
      category: category,
      data: result.news,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('خطأ في جلب أخبار الفئة:', error);
    res.status(500).json({
      error: 'خطأ في جلب أخبار الفئة',
      message: error.message
    });
  }
});

// GET /api/news/search/:query - Search news
router.get('/search/:query', async (req, res) => {
  try {
    const query = req.params.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    // Validate search query
    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        error: 'استعلام بحث غير صحيح',
        message: 'يجب أن يكون طول البحث أكثر من حرفين'
      });
    }

    const result = await News.search(query.trim(), page, limit);
    
    res.json({
      success: true,
      query: query,
      data: result.news,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('خطأ في البحث:', error);
    res.status(500).json({
      error: 'خطأ في البحث',
      message: error.message
    });
  }
});

// GET /api/news/stats - Get statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await News.getStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    res.status(500).json({
      error: 'خطأ في جلب الإحصائيات',
      message: error.message
    });
  }
});

// GET /api/news/admin/trigger - Manual trigger for scraping (admin only)
router.post('/admin/trigger', async (req, res) => {
  try {
    const { triggerManualRun } = require('../utils/scheduler');
    await triggerManualRun();

    res.json({
      success: true,
      message: 'تم تشغيل جمع الأخبار يدوياً'
    });
  } catch (error) {
    console.error('خطأ في التشغيل اليدوي:', error);
    res.status(500).json({
      error: 'خطأ في التشغيل اليدوي',
      message: error.message
    });
  }
});

// GET /api/news/admin/status - Get scheduler status
router.get('/admin/status', async (req, res) => {
  try {
    const { getScraperStats } = require('../utils/scheduler');
    const stats = await getScraperStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('خطأ في جلب حالة الجدولة:', error);
    res.status(500).json({
      error: 'خطأ في جلب حالة الجدولة',
      message: error.message
    });
  }
});

// GET /api/news/:id - Get specific news article (must be last)
router.get('/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    if (!id || id < 1) {
      return res.status(400).json({
        error: 'معرف غير صحيح',
        message: 'يجب أن يكون معرف الخبر رقماً صحيحاً'
      });
    }

    const news = await News.getById(id);

    if (!news) {
      return res.status(404).json({
        error: 'الخبر غير موجود',
        message: 'لم يتم العثور على الخبر المطلوب'
      });
    }

    res.json({
      success: true,
      data: news
    });
  } catch (error) {
    console.error('خطأ في جلب الخبر:', error);
    res.status(500).json({
      error: 'خطأ في جلب الخبر',
      message: error.message
    });
  }
});

module.exports = router;
