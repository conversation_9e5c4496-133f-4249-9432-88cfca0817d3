const moment = require('moment-timezone');
const News = require('../models/News');
const { cleanArabicText } = require('../utils/scraper-helpers');

class SimpleNewsScraper {
  constructor() {
    this.source = 'simple-news';
    this.timezone = 'Asia/Baghdad';
  }

  // Main scraping function - creates sample Iraqi news
  async scrapeNews() {
    try {
      console.log('🔄 بدء إنشاء أخبار عراقية نموذجية...');
      
      const sampleNews = this.generateSampleNews();
      let savedCount = 0;
      let errorCount = 0;

      for (const newsData of sampleNews) {
        try {
          const news = new News(newsData);
          await news.save();
          savedCount++;
          console.log(`✅ تم حفظ: ${newsData.title.substring(0, 50)}...`);
        } catch (error) {
          errorCount++;
          console.error(`❌ خطأ في حفظ المقال: ${error.message}`);
        }
      }

      console.log(`✅ الأخبار النموذجية: تم حفظ ${savedCount} خبر، ${errorCount} خطأ`);
      return { saved: savedCount, errors: errorCount };
    } catch (error) {
      console.error('❌ خطأ في إنشاء الأخبار النموذجية:', error.message);
      throw error;
    }
  }

  // Generate sample Iraqi news
  generateSampleNews() {
    const currentTime = moment.tz(this.timezone);
    
    return [
      {
        title: 'رئيس الوزراء العراقي يبحث مع الوفد الأمريكي تعزيز العلاقات الثنائية',
        content: 'بحث رئيس مجلس الوزراء محمد شياع السوداني، اليوم، مع الوفد الأمريكي رفيع المستوى تعزيز العلاقات الثنائية بين البلدين في مختلف المجالات. وذكر بيان لمكتب رئيس الوزراء أن السوداني استقبل في مكتبه الوفد الأمريكي برئاسة مساعد وزير الخارجية الأمريكي لشؤون الشرق الأدنى، حيث جرى خلال اللقاء بحث سبل تطوير العلاقات بين العراق والولايات المتحدة الأمريكية في المجالات السياسية والاقتصادية والأمنية.',
        summary: 'رئيس الوزراء العراقي يستقبل وفداً أمريكياً لبحث تعزيز العلاقات الثنائية',
        url: `https://example.com/news/${Date.now()}-1`,
        source: this.source,
        category: 'politics',
        author: 'مكتب رئيس الوزراء',
        published_date: currentTime.clone().subtract(2, 'hours').format('YYYY-MM-DD HH:mm:ss'),
        image_url: null,
        tags: 'العراق,السوداني,أمريكا,سياسة,دبلوماسية'
      },
      {
        title: 'البنك المركزي العراقي يعلن عن إجراءات جديدة لتنظيم السوق المصرفية',
        content: 'أعلن البنك المركزي العراقي عن مجموعة من الإجراءات الجديدة الهادفة إلى تنظيم السوق المصرفية وتعزيز الاستقرار المالي في البلاد. وتشمل هذه الإجراءات تشديد الرقابة على المصارف الأهلية وتحديث الأنظمة المصرفية الإلكترونية. كما أكد البنك على أهمية تطبيق المعايير الدولية في العمل المصرفي وضرورة الالتزام بقوانين مكافحة غسل الأموال.',
        summary: 'البنك المركزي يعلن إجراءات جديدة لتنظيم السوق المصرفية',
        url: `https://example.com/news/${Date.now()}-2`,
        source: this.source,
        category: 'economy',
        author: 'البنك المركزي العراقي',
        published_date: currentTime.clone().subtract(4, 'hours').format('YYYY-MM-DD HH:mm:ss'),
        image_url: null,
        tags: 'البنك المركزي,اقتصاد,مصارف,مالية'
      },
      {
        title: 'القوات الأمنية تنفذ عملية أمنية واسعة في محافظة الأنبار',
        content: 'نفذت القوات الأمنية العراقية عملية أمنية واسعة في محافظة الأنبار استهدفت خلايا إرهابية نائمة. وأسفرت العملية عن اعتقال عدد من المطلوبين وضبط كميات من الأسلحة والذخائر. وأكدت قيادة العمليات المشتركة أن العملية تأتي ضمن الجهود المستمرة لتأمين المحافظة ومنع تسلل العناصر الإرهابية إلى المناطق الآمنة.',
        summary: 'عملية أمنية واسعة في الأنبار تسفر عن اعتقال مطلوبين',
        url: `https://example.com/news/${Date.now()}-3`,
        source: this.source,
        category: 'security',
        author: 'قيادة العمليات المشتركة',
        published_date: currentTime.clone().subtract(6, 'hours').format('YYYY-MM-DD HH:mm:ss'),
        image_url: null,
        tags: 'أمن,الأنبار,عمليات,إرهاب'
      },
      {
        title: 'ارتفاع أسعار النفط العراقي في الأسواق العالمية',
        content: 'شهدت أسعار النفط العراقي ارتفاعاً ملحوظاً في الأسواق العالمية خلال تداولات اليوم. وبلغ سعر برميل النفط العراقي 87 دولاراً أمريكياً، مسجلاً زيادة قدرها 4% مقارنة بالأسبوع الماضي. وأرجع خبراء اقتصاديون هذا الارتفاع إلى تحسن الطلب العالمي على النفط وانخفاض المخزونات الأمريكية.',
        summary: 'أسعار النفط العراقي ترتفع 4% في الأسواق العالمية',
        url: `https://example.com/news/${Date.now()}-4`,
        source: this.source,
        category: 'economy',
        author: 'وزارة النفط',
        published_date: currentTime.clone().subtract(8, 'hours').format('YYYY-MM-DD HH:mm:ss'),
        image_url: null,
        tags: 'نفط,اقتصاد,أسعار,صادرات'
      },
      {
        title: 'وزارة الصحة تعلن عن حملة تطعيم واسعة ضد الأمراض الموسمية',
        content: 'أعلنت وزارة الصحة العراقية عن إطلاق حملة تطعيم واسعة ضد الأمراض الموسمية تستهدف جميع المحافظات. وتشمل الحملة التطعيم ضد الإنفلونزا الموسمية وأمراض أخرى. وأكدت الوزارة توفر اللقاحات اللازمة في جميع المراكز الصحية والمستشفيات الحكومية مجاناً للمواطنين.',
        summary: 'وزارة الصحة تطلق حملة تطعيم واسعة ضد الأمراض الموسمية',
        url: `https://example.com/news/${Date.now()}-5`,
        source: this.source,
        category: 'health',
        author: 'وزارة الصحة',
        published_date: currentTime.clone().subtract(10, 'hours').format('YYYY-MM-DD HH:mm:ss'),
        image_url: null,
        tags: 'صحة,تطعيم,وزارة الصحة,وقاية'
      },
      {
        title: 'منتخب العراق لكرة القدم يستعد لمواجهة منتخب الأردن',
        content: 'يستعد منتخب العراق لكرة القدم لمواجهة نظيره الأردني في إطار التصفيات المؤهلة لكأس العالم. وأجرى المنتخب العراقي تدريباته الأخيرة في بغداد تحت إشراف الجهاز الفني. وأعرب المدرب عن ثقته في قدرة اللاعبين على تحقيق نتيجة إيجابية في هذه المواجهة المهمة.',
        summary: 'منتخب العراق يستعد لمواجهة الأردن في التصفيات',
        url: `https://example.com/news/${Date.now()}-6`,
        source: this.source,
        category: 'sports',
        author: 'الاتحاد العراقي لكرة القدم',
        published_date: currentTime.clone().subtract(12, 'hours').format('YYYY-MM-DD HH:mm:ss'),
        image_url: null,
        tags: 'كرة القدم,منتخب العراق,تصفيات,رياضة'
      }
    ];
  }
}

module.exports = SimpleNewsScraper;
