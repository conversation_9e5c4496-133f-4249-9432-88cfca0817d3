const cheerio = require('cheerio');
const moment = require('moment-timezone');
const News = require('../models/News');
const { makeRequest, cleanArabicText, detectCategoryFromText, globalRateLimiter } = require('../utils/scraper-helpers');

class HathalyoumScraper {
  constructor() {
    this.baseUrl = 'https://hathalyoum.net';
    this.source = 'hathalyoum';
    this.timezone = 'Asia/Baghdad';
  }

  // Main scraping function
  async scrapeNews() {
    try {
      console.log('🔄 بدء جمع الأخبار من هذا اليوم...');
      
      const articles = await this.getArticlesList();
      let savedCount = 0;
      let errorCount = 0;

      for (const article of articles) {
        try {
          const fullArticle = await this.getArticleDetails(article);
          if (fullArticle) {
            const news = new News(fullArticle);
            await news.save();
            savedCount++;
            console.log(`✅ تم حفظ: ${fullArticle.title.substring(0, 50)}...`);
          }
        } catch (error) {
          errorCount++;
          console.error(`❌ خطأ في حفظ المقال: ${error.message}`);
        }
      }

      console.log(`✅ هذا اليوم: تم حفظ ${savedCount} خبر، ${errorCount} خطأ`);
      return { saved: savedCount, errors: errorCount };
    } catch (error) {
      console.error('❌ خطأ في جمع أخبار هذا اليوم:', error.message);
      throw error;
    }
  }

  // Get list of articles from main page
  async getArticlesList() {
    try {
      await globalRateLimiter.waitIfNeeded();
      const response = await makeRequest(this.baseUrl);

      const $ = cheerio.load(response.data);
      const articles = [];

      // Extract articles from main page
      $('.post, .news-item, .article, .story').each((index, element) => {
        const $element = $(element);
        const titleElement = $element.find('h1, h2, h3, h4, .title, .headline').first();
        const linkElement = $element.find('a').first();
        
        if (titleElement.length && linkElement.length) {
          const title = titleElement.text().trim();
          const relativeUrl = linkElement.attr('href');
          
          if (title && relativeUrl) {
            const fullUrl = relativeUrl.startsWith('http') ? relativeUrl : `${this.baseUrl}${relativeUrl}`;
            
            articles.push({
              title: title,
              url: fullUrl,
              summary: $element.find('.excerpt, .summary, .description').text().trim() || null
            });
          }
        }
      });

      // Try alternative selectors
      $('article, .news-card, .post-card, .content-item').each((index, element) => {
        const $element = $(element);
        const titleElement = $element.find('h1, h2, h3, .post-title, .entry-title').first();
        const linkElement = $element.find('a[href*="/"]').first();
        
        if (titleElement.length && linkElement.length) {
          const title = titleElement.text().trim();
          const relativeUrl = linkElement.attr('href');
          
          if (title && relativeUrl && title.length > 10) {
            const fullUrl = relativeUrl.startsWith('http') ? relativeUrl : `${this.baseUrl}${relativeUrl}`;
            
            // Avoid duplicates
            if (!articles.some(article => article.url === fullUrl)) {
              articles.push({
                title: title,
                url: fullUrl,
                summary: $element.find('.excerpt, .summary, .content').text().trim().substring(0, 200) || null
              });
            }
          }
        }
      });

      // Extract from news lists
      $('.news-list li, .article-list li, ul.posts li').each((index, element) => {
        const $element = $(element);
        const linkElement = $element.find('a').first();
        
        if (linkElement.length) {
          const title = linkElement.text().trim() || linkElement.attr('title');
          const relativeUrl = linkElement.attr('href');
          
          if (title && relativeUrl && title.length > 10) {
            const fullUrl = relativeUrl.startsWith('http') ? relativeUrl : `${this.baseUrl}${relativeUrl}`;
            
            if (!articles.some(article => article.url === fullUrl)) {
              articles.push({
                title: title,
                url: fullUrl,
                summary: null
              });
            }
          }
        }
      });

      console.log(`📰 هذا اليوم: تم العثور على ${articles.length} مقال`);
      return articles.slice(0, 20); // Limit to 20 articles per scrape
    } catch (error) {
      console.error('خطأ في جلب قائمة المقالات من هذا اليوم:', error.message);
      return [];
    }
  }

  // Get full article details
  async getArticleDetails(article) {
    try {
      await globalRateLimiter.waitIfNeeded();
      const response = await makeRequest(article.url);

      const $ = cheerio.load(response.data);
      
      // Extract content
      const content = this.extractContent($);
      const publishedDate = this.extractPublishedDate($);
      const author = this.extractAuthor($);
      const category = this.extractCategory($);
      const imageUrl = this.extractImageUrl($);
      const tags = this.extractTags($);

      return {
        title: cleanArabicText(article.title),
        content: cleanArabicText(content),
        summary: cleanArabicText(article.summary) || cleanArabicText(content).substring(0, 200) + '...',
        url: article.url,
        source: this.source,
        category: category || detectCategoryFromText(article.title + ' ' + content),
        author: author,
        published_date: publishedDate,
        image_url: imageUrl,
        tags: tags
      };
    } catch (error) {
      console.error(`خطأ في جلب تفاصيل المقال ${article.url}:`, error.message);
      return null;
    }
  }

  // Extract article content
  extractContent($) {
    const contentSelectors = [
      '.post-content',
      '.entry-content',
      '.article-content',
      '.content',
      '.story-content',
      '.news-content',
      '.main-content',
      'article .content',
      '.post-body'
    ];

    for (const selector of contentSelectors) {
      const content = $(selector).text().trim();
      if (content && content.length > 100) {
        // Clean up content
        return content.replace(/\s+/g, ' ').trim();
      }
    }

    // Fallback: get all paragraphs
    const paragraphs = $('p').map((i, el) => $(el).text().trim()).get();
    const validParagraphs = paragraphs.filter(p => p.length > 20);
    return validParagraphs.join('\n\n');
  }

  // Extract published date
  extractPublishedDate($) {
    const dateSelectors = [
      '.post-date',
      '.date',
      '.publish-date',
      '.article-date',
      'time',
      '.timestamp',
      '.entry-date',
      '[datetime]'
    ];

    for (const selector of dateSelectors) {
      const dateElement = $(selector).first();
      if (dateElement.length) {
        const dateText = dateElement.attr('datetime') || dateElement.text().trim();
        
        // Try to parse different date formats
        const formats = [
          'YYYY-MM-DD HH:mm:ss',
          'YYYY-MM-DD',
          'DD/MM/YYYY',
          'MM/DD/YYYY',
          'YYYY/MM/DD'
        ];
        
        for (const format of formats) {
          const parsedDate = moment.tz(dateText, format, this.timezone);
          if (parsedDate.isValid()) {
            return parsedDate.format('YYYY-MM-DD HH:mm:ss');
          }
        }
      }
    }

    // Default to current time if no date found
    return moment.tz(this.timezone).format('YYYY-MM-DD HH:mm:ss');
  }

  // Extract author
  extractAuthor($) {
    const authorSelectors = [
      '.author',
      '.by-author',
      '.post-author',
      '.article-author',
      '.writer',
      '.byline',
      '.author-name'
    ];

    for (const selector of authorSelectors) {
      const author = $(selector).text().trim();
      if (author) {
        return author.replace(/^(بقلم|كتب|المحرر|الكاتب|بواسطة):?\s*/i, '');
      }
    }

    return 'هذا اليوم';
  }

  // Extract category
  extractCategory($) {
    const categorySelectors = [
      '.category',
      '.post-category',
      '.article-category',
      '.section',
      '.breadcrumb a:last-child',
      '.cat-links a',
      '.entry-category'
    ];

    for (const selector of categorySelectors) {
      const category = $(selector).text().trim();
      if (category) {
        return this.mapCategory(category);
      }
    }

    // Try to extract from URL
    const urlParts = new URL(this.baseUrl).pathname.split('/');
    for (const part of urlParts) {
      if (part) {
        const mappedCategory = this.mapCategory(part);
        if (mappedCategory !== 'general') {
          return mappedCategory;
        }
      }
    }

    return 'general';
  }

  // Extract image URL
  extractImageUrl($) {
    const imageSelectors = [
      '.post-image img',
      '.article-image img',
      '.featured-image img',
      '.entry-image img',
      '.content img:first',
      'article img:first',
      '.thumbnail img'
    ];

    for (const selector of imageSelectors) {
      const img = $(selector).first();
      if (img.length) {
        const src = img.attr('src') || img.attr('data-src') || img.attr('data-lazy-src');
        if (src) {
          return src.startsWith('http') ? src : `${this.baseUrl}${src}`;
        }
      }
    }

    return null;
  }

  // Extract tags
  extractTags($) {
    const tags = [];
    
    $('.tag, .tags a, .post-tags a, .entry-tags a').each((i, el) => {
      const tag = $(el).text().trim();
      if (tag) tags.push(tag);
    });

    return tags.length > 0 ? tags.join(',') : null;
  }

  // Map Arabic categories to English
  mapCategory(arabicCategory) {
    const categoryMap = {
      'سياسة': 'politics',
      'سياسي': 'politics',
      'اقتصاد': 'economy',
      'اقتصادي': 'economy',
      'أمن': 'security',
      'أمني': 'security',
      'رياضة': 'sports',
      'رياضي': 'sports',
      'ثقافة': 'culture',
      'ثقافي': 'culture',
      'تكنولوجيا': 'technology',
      'تقنية': 'technology',
      'صحة': 'health',
      'طب': 'health',
      'محلي': 'general',
      'محلية': 'general',
      'عربي': 'general',
      'دولي': 'general',
      'عام': 'general'
    };

    const lowerCategory = arabicCategory.toLowerCase();
    for (const [arabic, english] of Object.entries(categoryMap)) {
      if (lowerCategory.includes(arabic)) {
        return english;
      }
    }

    return 'general';
  }
}

module.exports = HathalyoumScraper;
