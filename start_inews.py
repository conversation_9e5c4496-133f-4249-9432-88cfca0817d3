#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تشغيل نظام أخبار العراق iNews
"""

import os
import sys
import subprocess
import time

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    try:
        import requests
        import flask
        import feedparser
        import beautifulsoup4
        import pytz
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("📦 تثبيت المتطلبات...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        except Exception as install_error:
            print(f"❌ فشل في تثبيت المتطلبات: {install_error}")
            return False

def check_database():
    """فحص قاعدة البيانات"""
    print("🗄️ فحص قاعدة البيانات...")
    
    if os.path.exists('iraqi_news.db'):
        print("✅ قاعدة البيانات موجودة")
        return True
    else:
        print("⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها تلقائياً")
        return True

def start_server():
    """تشغيل الخادم"""
    print("🚀 بدء تشغيل خادم iNews...")
    print("📍 الخادم سيعمل على: http://localhost:5020")
    print("🔗 صفحة الأخبار: http://localhost:5020/news")
    print("⚙️ إدارة المصادر: http://localhost:5020/sources")
    print("🛠️ لوحة الإدارة: http://localhost:5020/admin")
    print("\n" + "="*50)
    print("💡 نصائح:")
    print("   • اضغط Ctrl+C لإيقاف الخادم")
    print("   • النظام يجمع الأخبار تلقائياً كل دقيقة")
    print("   • يمكنك إدارة المصادر من صفحة /sources")
    print("="*50 + "\n")
    
    try:
        # تشغيل التطبيق
        subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    print("🇮🇶 نظام أخبار العراق - iNews")
    print("=" * 40)
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات")
        return
    
    # فحص قاعدة البيانات
    if not check_database():
        print("❌ فشل في فحص قاعدة البيانات")
        return
    
    # تشغيل الخادم
    start_server()

if __name__ == '__main__':
    main()
