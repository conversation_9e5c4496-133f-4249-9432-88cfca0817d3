#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة مصادر إخبارية عراقية بديلة موثوقة
"""

import requests
import json
import time
from database import Database
from bs4 import BeautifulSoup

class AlternativeIraqiSources:
    def __init__(self):
        """تهيئة مدير المصادر البديلة"""
        self.db = Database()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ar,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
    
    def get_alternative_sources(self):
        """قائمة المصادر البديلة الموثوقة"""
        return [
            # مصادر حكومية وشبه رسمية
            {
                'name': 'شبكة الإعلام العراقي',
                'urls': [
                    'https://imn.iq/',
                    'http://imn.iq/',
                    'https://www.imn.iq/',
                    'http://www.imn.iq/'
                ],
                'type': 'website',
                'description': 'شبكة الإعلام العراقي الرسمية',
                'priority': 1
            },
            {
                'name': 'وكالة نينا',
                'urls': [
                    'https://ninanews.com/',
                    'http://ninanews.com/',
                    'https://www.ninanews.com/',
                    'http://www.ninanews.com/'
                ],
                'type': 'website',
                'description': 'وكالة الأنباء الوطنية العراقية',
                'priority': 1
            },
            {
                'name': 'العراقية نيوز',
                'urls': [
                    'https://www.iraqinews.com/',
                    'http://www.iraqinews.com/',
                    'https://iraqinews.com/',
                    'http://iraqinews.com/'
                ],
                'type': 'website',
                'description': 'موقع العراقية نيوز',
                'priority': 1
            },
            {
                'name': 'هذا اليوم',
                'urls': [
                    'https://hathalyoum.net/',
                    'http://hathalyoum.net/',
                    'https://www.hathalyoum.net/',
                    'http://www.hathalyoum.net/'
                ],
                'type': 'website',
                'description': 'موقع هذا اليوم الإخباري',
                'priority': 1
            },
            {
                'name': '964 ميديا',
                'urls': [
                    'https://964media.com/',
                    'http://964media.com/',
                    'https://www.964media.com/',
                    'http://www.964media.com/'
                ],
                'type': 'website',
                'description': 'شبكة 964 الإعلامية',
                'priority': 1
            },
            {
                'name': 'آي كيو نيوز',
                'urls': [
                    'https://www.iqnews.iq/',
                    'http://www.iqnews.iq/',
                    'https://iqnews.iq/',
                    'http://iqnews.iq/'
                ],
                'type': 'website',
                'description': 'موقع آي كيو نيوز',
                'priority': 2
            },
            {
                'name': 'الجبال نيوز',
                'urls': [
                    'https://aljeebal.com/',
                    'http://aljeebal.com/',
                    'https://www.aljeebal.com/',
                    'http://www.aljeebal.com/'
                ],
                'type': 'website',
                'description': 'موقع الجبال نيوز',
                'priority': 2
            },
            {
                'name': 'UTV العراق',
                'urls': [
                    'https://utviraq.net/',
                    'http://utviraq.net/',
                    'https://www.utviraq.net/',
                    'http://www.utviraq.net/'
                ],
                'type': 'website',
                'description': 'قناة UTV العراقية',
                'priority': 2
            },
            {
                'name': 'الأولى نيوز',
                'urls': [
                    'https://alawla.tv/',
                    'http://alawla.tv/',
                    'https://www.alawla.tv/',
                    'http://www.alawla.tv/'
                ],
                'type': 'website',
                'description': 'قناة الأولى العراقية',
                'priority': 2
            },
            {
                'name': 'الرابعة نيوز',
                'urls': [
                    'https://alrabiaa.tv/',
                    'http://alrabiaa.tv/',
                    'https://www.alrabiaa.tv/',
                    'http://www.alrabiaa.tv/'
                ],
                'type': 'website',
                'description': 'قناة الرابعة العراقية',
                'priority': 2
            },
            {
                'name': 'الغد برس',
                'urls': [
                    'https://alghadpress.com/',
                    'http://alghadpress.com/',
                    'https://www.alghadpress.com/',
                    'http://www.alghadpress.com/'
                ],
                'type': 'website',
                'description': 'موقع الغد برس',
                'priority': 3
            },
            {
                'name': 'عراق أوبزيرفر',
                'urls': [
                    'https://observeriraq.net/',
                    'http://observeriraq.net/',
                    'https://www.observeriraq.net/',
                    'http://www.observeriraq.net/'
                ],
                'type': 'website',
                'description': 'موقع عراق أوبزيرفر',
                'priority': 3
            },
            {
                'name': 'تايم نيوز',
                'urls': [
                    'https://timenews.iq/',
                    'http://timenews.iq/',
                    'https://www.timenews.iq/',
                    'http://www.timenews.iq/'
                ],
                'type': 'website',
                'description': 'موقع تايم نيوز العراقي',
                'priority': 3
            }
        ]
    
    def test_source_urls(self, source):
        """اختبار روابط مختلفة للمصدر"""
        print(f"\n🔍 اختبار {source['name']}:")
        
        for i, url in enumerate(source['urls'], 1):
            try:
                print(f"  {i}. اختبار: {url}")
                
                response = self.session.get(url, timeout=15, allow_redirects=True)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    
                    if 'html' in content_type:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        title = soup.find('title')
                        
                        if title and title.get_text().strip():
                            # فحص وجود محتوى إخباري
                            news_elements = soup.select('article, .news, .post, .entry, h1, h2, h3')
                            
                            if len(news_elements) > 3:
                                print(f"    ✅ يعمل - العنوان: {title.get_text().strip()[:50]}...")
                                print(f"    📊 عناصر إخبارية: {len(news_elements)}")
                                
                                return {
                                    'url': url,
                                    'title': title.get_text().strip(),
                                    'news_count': len(news_elements),
                                    'final_url': response.url
                                }
                            else:
                                print(f"    ⚠️ محتوى قليل - {len(news_elements)} عنصر")
                        else:
                            print(f"    ❌ لا يوجد عنوان")
                    else:
                        print(f"    ❌ ليس HTML: {content_type}")
                else:
                    print(f"    ❌ HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"    ❌ انتهت مهلة الاتصال")
            except requests.exceptions.ConnectionError:
                print(f"    ❌ خطأ في الاتصال")
            except Exception as e:
                print(f"    ❌ خطأ: {str(e)}")
            
            time.sleep(1)
        
        return None
    
    def get_enhanced_selectors(self, source_name):
        """الحصول على محددات CSS محسنة حسب المصدر"""
        
        # محددات مخصصة للمواقع المعروفة
        custom_selectors = {
            'شبكة الإعلام العراقي': {
                'container': '.news-item, .post, article, .entry, .news-card, .item',
                'title': 'h1, h2, h3, .title, .news-title, .post-title, .card-title',
                'content': '.content, .summary, .excerpt, p, .card-text, .news-content',
                'link': 'a'
            },
            'وكالة نينا': {
                'container': '.news-item, .post, article, .entry, .item',
                'title': 'h1, h2, h3, .title, .news-title, .headline',
                'content': '.content, .summary, .text, p, .news-content',
                'link': 'a'
            },
            'العراقية نيوز': {
                'container': '.news-item, .post, article, .entry, .news-card',
                'title': 'h1, h2, h3, .title, .news-title, .post-title',
                'content': '.content, .summary, .excerpt, p, .news-content',
                'link': 'a'
            },
            'هذا اليوم': {
                'container': '.news-item, .post, article, .entry, .item',
                'title': 'h1, h2, h3, .title, .news-title, .item-title',
                'content': '.content, .summary, .excerpt, p, .item-content',
                'link': 'a'
            },
            '964 ميديا': {
                'container': '.news-item, .post, article, .entry, .wire-item',
                'title': 'h1, h2, h3, .title, .news-title, .wire-title',
                'content': '.content, .summary, .excerpt, p, .wire-content',
                'link': 'a'
            }
        }
        
        # إذا كان هناك محددات مخصصة
        if source_name in custom_selectors:
            return custom_selectors[source_name]
        
        # محددات عامة محسنة
        return {
            'container': 'article, .article, .news-item, .post, .entry, .news, .item, .story, .card, .content-item, .news-box, .post-item',
            'title': 'h1, h2, h3, h4, .title, .headline, .post-title, .news-title, .article-title, .story-title, .entry-title, .item-title',
            'content': 'p, .content, .summary, .excerpt, .description, .text, .body, .news-content, .post-content, .article-content',
            'link': 'a, .link, .news-link, .post-link, .article-link, .story-link'
        }
    
    def add_alternative_sources(self):
        """إضافة المصادر البديلة بعد فحصها"""
        sources_list = self.get_alternative_sources()
        
        # ترتيب حسب الأولوية
        sources_list.sort(key=lambda x: x['priority'])
        
        added_count = 0
        failed_count = 0
        
        print("🚀 بدء إضافة المصادر البديلة الموثوقة...")
        print(f"📊 إجمالي المصادر للفحص: {len(sources_list)}")
        
        for source in sources_list:
            try:
                # اختبار روابط المصدر
                working_url_info = self.test_source_urls(source)
                
                if working_url_info:
                    # إعداد بيانات المصدر
                    selectors = self.get_enhanced_selectors(source['name'])
                    
                    source_data = {
                        'name': source['name'],
                        'url': working_url_info['url'],
                        'type': source['type'],
                        'is_active': True,
                        'selectors': json.dumps(selectors, ensure_ascii=False),
                        'description': f"{source['description']} - {working_url_info['title'][:100]}"
                    }
                    
                    # إضافة المصدر إلى قاعدة البيانات
                    if self.db.add_source(source_data):
                        added_count += 1
                        print(f"✅ تم إضافة: {source['name']}")
                    else:
                        print(f"⚠️ موجود مسبقاً: {source['name']}")
                else:
                    failed_count += 1
                    print(f"❌ فشل: {source['name']} - لا يوجد رابط يعمل")
                
                time.sleep(2)  # توقف بين المصادر
                
            except Exception as e:
                failed_count += 1
                print(f"❌ خطأ في {source['name']}: {str(e)}")
        
        print(f"\n🎉 انتهت إضافة المصادر البديلة:")
        print(f"✅ تم إضافة: {added_count} مصدر")
        print(f"❌ فشل: {failed_count} مصدر")
        print(f"📊 إجمالي المصادر المفحوصة: {len(sources_list)}")
        
        return added_count, failed_count
    
    def add_working_rss_feeds(self):
        """إضافة تغذيات RSS التي تعمل"""
        print("\n📡 إضافة تغذيات RSS موثوقة...")
        
        rss_feeds = [
            {
                'name': 'الجزيرة نت RSS',
                'url': 'https://www.aljazeera.net/rss/all',
                'description': 'تغذية RSS للجزيرة نت'
            },
            {
                'name': 'BBC عربي RSS',
                'url': 'https://feeds.bbci.co.uk/arabic/rss.xml',
                'description': 'تغذية RSS لبي بي سي عربي'
            },
            {
                'name': 'العربية نت RSS',
                'url': 'https://www.alarabiya.net/rss',
                'description': 'تغذية RSS للعربية نت'
            },
            {
                'name': 'سكاي نيوز عربية RSS',
                'url': 'https://www.skynewsarabia.com/rss',
                'description': 'تغذية RSS لسكاي نيوز عربية'
            }
        ]
        
        added_rss = 0
        
        for rss_feed in rss_feeds:
            try:
                print(f"📡 اختبار: {rss_feed['name']}")
                
                response = self.session.get(rss_feed['url'], timeout=10)
                
                if response.status_code == 200:
                    import feedparser
                    feed = feedparser.parse(response.content)
                    
                    if feed.bozo == 0 and len(feed.entries) > 0:
                        print(f"    ✅ RSS صحيح - {len(feed.entries)} عنصر")
                        
                        rss_data = {
                            'name': rss_feed['name'],
                            'url': rss_feed['url'],
                            'type': 'rss',
                            'is_active': True,
                            'selectors': '',
                            'description': rss_feed['description']
                        }
                        
                        if self.db.add_source(rss_data):
                            added_rss += 1
                            print(f"    ✅ تم إضافة RSS: {rss_feed['name']}")
                        else:
                            print(f"    ⚠️ RSS موجود مسبقاً: {rss_feed['name']}")
                    else:
                        print(f"    ❌ RSS غير صحيح")
                else:
                    print(f"    ❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ خطأ: {str(e)}")
            
            time.sleep(1)
        
        print(f"📡 تم إضافة {added_rss} تغذية RSS")
        return added_rss

if __name__ == '__main__':
    manager = AlternativeIraqiSources()
    
    # إضافة المصادر البديلة
    added, failed = manager.add_alternative_sources()
    
    # إضافة تغذيات RSS
    rss_added = manager.add_working_rss_feeds()
    
    print(f"\n🎊 النتيجة النهائية:")
    print(f"✅ مصادر مضافة: {added}")
    print(f"❌ مصادر فشلت: {failed}")
    print(f"📡 RSS مضاف: {rss_added}")
    print(f"📊 إجمالي المصادر الجديدة: {added + rss_added}")
