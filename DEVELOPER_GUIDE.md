# دليل المطور - API الأخبار العراقية

## نظرة عامة

هذا API مصمم لجمع وتوفير الأخبار من المواقع الإخبارية العراقية والعربية. يستخدم تقنيات web scraping و RSS feeds لجمع الأخبار تلقائياً.

## البنية التقنية

### التقنيات المستخدمة
- **Node.js** - بيئة التشغيل
- **Express.js** - إطار عمل الويب
- **SQLite** - قاعدة البيانات
- **Cheerio** - تحليل HTML
- **Axios** - طلبات HTTP
- **node-cron** - جدولة المهام
- **Moment.js** - معالجة التواريخ

### بنية المشروع

```
iraqi-news-api/
├── config/
│   └── database.js          # إعدادات قاعدة البيانات
├── models/
│   └── News.js              # نموذج البيانات
├── routes/
│   └── news.js              # مسارات API
├── scrapers/                # جامعات الأخبار
│   ├── shafaq.js           # شفق نيوز
│   ├── hathalyoum.js       # هذا اليوم
│   ├── media964.js         # 964 ميديا
│   ├── alsumaria.js        # السومرية
│   └── rss-scraper.js      # RSS feeds
├── utils/
│   ├── scheduler.js        # نظام الجدولة
│   ├── scraper-helpers.js  # مساعدات الـ scraping
│   └── seed-data.js        # البيانات التجريبية
├── scripts/
│   └── seed.js             # سكريبت إدراج البيانات
├── data/                   # قاعدة البيانات
├── server.js               # الخادم الرئيسي
└── test-api.js             # اختبار API
```

## إضافة مصدر أخبار جديد

### 1. إنشاء Scraper جديد

```javascript
// scrapers/new-source.js
const cheerio = require('cheerio');
const moment = require('moment-timezone');
const News = require('../models/News');
const { makeRequest, cleanArabicText, detectCategoryFromText, globalRateLimiter } = require('../utils/scraper-helpers');

class NewSourceScraper {
  constructor() {
    this.baseUrl = 'https://example.com';
    this.source = 'new-source';
    this.timezone = 'Asia/Baghdad';
  }

  async scrapeNews() {
    // تنفيذ منطق جمع الأخبار
  }

  async getArticlesList() {
    // جلب قائمة المقالات
  }

  async getArticleDetails(article) {
    // جلب تفاصيل المقال
  }
}

module.exports = NewSourceScraper;
```

### 2. إضافة المصدر إلى Scheduler

```javascript
// utils/scheduler.js
const NewSourceScraper = require('../scrapers/new-source');

// في constructor
this.scrapers = {
  // المصادر الموجودة...
  newSource: new NewSourceScraper()
};
```

### 3. تحديث قاعدة البيانات

```javascript
// config/database.js
const sources = [
  // المصادر الموجودة...
  { name: 'new-source', url: 'https://example.com', scrape_interval: 20 }
];
```

### 4. تحديث API Routes

```javascript
// routes/news.js
const validSources = ['shafaq', 'hathalyoum', 'media964', 'alsumaria', 'new-source'];
```

## نموذج البيانات

### جدول الأخبار (news)

| العمود | النوع | الوصف |
|--------|------|-------|
| id | INTEGER | المعرف الفريد |
| title | TEXT | عنوان الخبر |
| content | TEXT | محتوى الخبر |
| summary | TEXT | ملخص الخبر |
| url | TEXT | رابط الخبر |
| source | TEXT | مصدر الخبر |
| category | TEXT | فئة الخبر |
| author | TEXT | كاتب الخبر |
| published_date | DATETIME | تاريخ النشر |
| scraped_date | DATETIME | تاريخ الجمع |
| image_url | TEXT | رابط الصورة |
| tags | TEXT | الكلمات المفتاحية |
| views | INTEGER | عدد المشاهدات |
| is_active | BOOLEAN | حالة النشاط |

## API Endpoints

### الأخبار الأساسية

```http
GET /api/news                    # جميع الأخبار
GET /api/news/latest             # أحدث الأخبار
GET /api/news/:id                # خبر محدد
GET /api/news/stats              # الإحصائيات
```

### التصفية والبحث

```http
GET /api/news/source/:source     # أخبار من مصدر محدد
GET /api/news/category/:category # أخبار حسب الفئة
GET /api/news/search/:query      # البحث في الأخبار
```

### الإدارة

```http
POST /api/news/admin/trigger     # تشغيل يدوي للجمع
GET /api/news/admin/status       # حالة النظام
```

## نظام الجدولة

### إعدادات افتراضية
- **تحديث الأخبار**: كل 30 دقيقة
- **تنظيف البيانات**: كل ساعتين
- **الاحتفاظ بالبيانات**: 30 يوم

### تخصيص الجدولة

```javascript
// utils/scheduler.js
// تغيير فترة التحديث
cron.schedule('*/15 * * * *', async () => {
  await this.runAllScrapers(); // كل 15 دقيقة
});
```

## معالجة الأخطاء

### أخطاء شائعة وحلولها

#### 403 Forbidden
```javascript
// استخدام headers متقدمة
const response = await makeRequest(url, {
  headers: {
    'User-Agent': 'Mozilla/5.0...',
    'Referer': 'https://google.com',
    'Accept-Language': 'ar,en'
  }
});
```

#### Rate Limiting
```javascript
// استخدام Rate Limiter
await globalRateLimiter.waitIfNeeded();
```

#### تحليل HTML
```javascript
// استخدام selectors متعددة
const contentSelectors = [
  '.article-content',
  '.post-content',
  '.entry-content'
];

for (const selector of contentSelectors) {
  const content = $(selector).text().trim();
  if (content && content.length > 100) {
    return content;
  }
}
```

## الأمان والأداء

### Rate Limiting
- 100 طلب كل 15 دقيقة لكل IP
- 20 طلب في الدقيقة للـ scrapers

### Caching
- ضغط الاستجابات
- Cache headers للمحتوى الثابت

### Security Headers
- Helmet.js للحماية
- CORS للطلبات المتقاطعة

## الاختبار

### تشغيل الاختبارات

```bash
# اختبار API شامل
npm run test-api

# اختبار مصدر محدد
node -e "
const Scraper = require('./scrapers/shafaq');
const scraper = new Scraper();
scraper.scrapeNews().then(console.log);
"
```

### إضافة بيانات تجريبية

```bash
# إدراج بيانات تجريبية
npm run seed

# مسح البيانات
npm run seed:clear

# إعادة تعيين البيانات
npm run seed:reset
```

## المراقبة والصيانة

### مراقبة الأداء

```javascript
// فحص حالة النظام
GET /health

// إحصائيات مفصلة
GET /api/news/admin/status
```

### صيانة قاعدة البيانات

```javascript
// حذف الأخبار القديمة
const News = require('./models/News');
await News.deleteOld(30); // أقدم من 30 يوم
```

## التطوير المحلي

### إعداد البيئة

```bash
# تثبيت التبعيات
npm install

# إعداد متغيرات البيئة
cp .env.example .env

# تشغيل الخادم
npm run dev
```

### Debug Mode

```bash
# تشغيل مع debug
DEBUG=* npm run dev

# مراقبة الملفات
npm run dev
```

## النشر

### متطلبات الإنتاج
- Node.js 16+
- 512MB RAM على الأقل
- 1GB مساحة تخزين

### متغيرات البيئة

```env
NODE_ENV=production
PORT=5020
DB_PATH=./data/iraqi_news.db
SCRAPE_INTERVAL_MINUTES=30
```

### PM2 Configuration

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'iraqi-news-api',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 5020
    }
  }]
};
```

## المساهمة

### إرشادات المساهمة
1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. كتابة اختبارات للكود الجديد
4. التأكد من تشغيل جميع الاختبارات
5. إنشاء Pull Request

### معايير الكود
- استخدام ESLint للتحقق من الكود
- تعليقات باللغة العربية
- معالجة شاملة للأخطاء
- logging مفصل للعمليات

## الدعم

للحصول على الدعم:
1. مراجعة هذا الدليل
2. فحص logs الخادم
3. إنشاء issue في المستودع
4. التواصل مع فريق التطوير
