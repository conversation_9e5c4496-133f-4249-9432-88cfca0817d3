#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام أخبار العراق مع فتح المتصفح تلقائياً بعد جهوزية السيرفر
"""

import os
import sys
import time
import threading
import webbrowser
import requests
import subprocess
from datetime import datetime

def check_requirements():
    """فحص وتثبيت المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_modules = [
        'flask', 'requests', 'beautifulsoup4', 
        'feedparser', 'schedule', 'python-dateutil', 
        'pytz', 'lxml', 'html5lib'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'beautifulsoup4':
                __import__('bs4')
            elif module == 'python-dateutil':
                __import__('dateutil')
            else:
                __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"📦 تثبيت المكتبات المفقودة: {', '.join(missing_modules)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                '--upgrade', '--quiet'
            ] + missing_modules)
            print("✅ تم تثبيت المتطلبات بنجاح")
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت المتطلبات")
            return False
    else:
        print("✅ جميع المتطلبات مثبتة")
    
    return True

def wait_for_server(url="http://localhost:5020", timeout=60):
    """انتظار حتى يصبح السيرفر جاهز<|im_start|>"""
    print("⏳ انتظار جهوزية السيرفر...")
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print("✅ السيرفر جاهز!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(1)
        print(".", end="", flush=True)
    
    print(f"\n❌ انتهت مهلة الانتظار ({timeout} ثانية)")
    return False

def open_browser_when_ready():
    """فتح المتصفح عندما يصبح السيرفر جاهز<|im_start|>"""
    url = "http://localhost:5020"
    
    if wait_for_server(url):
        print(f"🌐 فتح المتصفح: {url}")
        time.sleep(1)  # انتظار إضافي للتأكد
        webbrowser.open(url)
        
        # فتح صفحة الأخبار أيض<|im_start|> بعد 3 ثوانٍ
        time.sleep(3)
        print(f"📰 فتح صفحة الأخبار: {url}/news")
        webbrowser.open(f"{url}/news")
    else:
        print("❌ فشل في فتح المتصفح - السيرفر غير جاهز")

def start_server():
    """تشغيل السيرفر"""
    print("🚀 بدء تشغيل خادم iNews...")
    
    # تشغيل app.py
    try:
        os.system("python app.py")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف السيرفر")
    except Exception as e:
        print(f"❌ خطأ في تشغيل السيرفر: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🇮🇶 نظام أخبار العراق - iNews")
    print("=" * 40)
    print(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # فحص المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # فحص وجود الملفات المطلوبة
    required_files = ['app.py', 'database.py', 'news_scraper.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع الملفات موجودة")
    print()
    
    # بدء thread لفتح المتصفح
    browser_thread = threading.Thread(target=open_browser_when_ready, daemon=True)
    browser_thread.start()
    
    print("📍 الخادم سيعمل على: http://localhost:5020")
    print("🌐 المتصفح سيفتح تلقائ<|im_start|> عند جهوزية السيرفر")
    print("💡 اضغط Ctrl+C لإيقاف الخادم")
    print()
    print("=" * 50)
    print()
    
    # تشغيل السيرفر
    start_server()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إغلاق النظام")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {str(e)}")
        input("اضغط Enter للخروج...")
