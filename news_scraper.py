#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جامع الأخبار العراقية - Iraqi News Scraper
"""

import requests
import feedparser
from bs4 import BeautifulSoup
import logging
from datetime import datetime, timedelta
import pytz
from typing import List, Dict
import re
import time

logger = logging.getLogger(__name__)

class NewsScraper:
    def __init__(self, database):
        """تهيئة جامع الأخبار"""
        self.db = database
        self.iraq_tz = pytz.timezone('Asia/Baghdad')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # كلمات مفتاحية للأخبار العراقية
        self.iraqi_keywords = [
            'العراق', 'بغداد', 'البصرة', 'أربيل', 'الموصل', 'النجف', 'كربلاء', 'الأنبار',
            'ديالى', 'صلاح الدين', 'كركوك', 'واسط', 'ميسان', 'ذي قار', 'المثنى',
            'بابل', 'القادسية', 'دهوك', 'السليمانية', 'حلبجة', 'عراقي', 'عراقية',
            'الحكومة العراقية', 'البرلمان العراقي', 'رئيس الوزراء العراقي',
            'وزارة', 'محافظة', 'محافظ', 'نائب', 'وزير', 'مجلس النواب',
            'الكاظمي', 'السوداني', 'صالح', 'الحلبوسي', 'العبادي', 'المالكي',
            'الصدر', 'الحكيم', 'العامري', 'البارزاني', 'طالباني',
            'الحشد الشعبي', 'القوات الأمنية', 'الجيش العراقي', 'الشرطة العراقية',
            'دينار عراقي', 'البنك المركزي العراقي', 'النفط العراقي',
            'الكهرباء', 'الماء', 'الخدمات', 'الاستثمار', 'التجارة',
            'الأمن', 'الإرهاب', 'داعش', 'القاعدة', 'العمليات الأمنية'
        ]
        
        # كلمات مستبعدة (الفن، الثقافة، الطقس، الرياضة)
        self.excluded_keywords = [
            'فن', 'فنان', 'فنانة', 'مسرح', 'سينما', 'فيلم', 'مسلسل', 'أغنية', 'موسيقى',
            'ثقافة', 'ثقافي', 'مهرجان', 'معرض', 'كتاب', 'شاعر', 'أدب', 'أديب',
            'طقس', 'أمطار', 'حرارة', 'رياح', 'عاصفة', 'ثلوج', 'مناخ',
            'رياضة', 'كرة', 'لاعب', 'مباراة', 'بطولة', 'دوري', 'منتخب', 'نادي',
            'أولمبياد', 'كأس', 'هدف', 'تسجيل', 'فوز', 'خسارة', 'تعادل'
        ]
    
    def is_iraqi_news(self, title: str, content: str = '') -> bool:
        """فحص ما إذا كان الخبر عراقي"""
        text = f"{title} {content}".lower()
        
        # فحص الكلمات المستبعدة أولاً
        for keyword in self.excluded_keywords:
            if keyword in text:
                return False
        
        # فحص الكلمات العراقية
        for keyword in self.iraqi_keywords:
            if keyword in text:
                return True
        
        return False
    
    def categorize_news(self, title: str, content: str = '') -> str:
        """تصنيف الأخبار"""
        text = f"{title} {content}".lower()
        
        political_keywords = ['سياسة', 'حكومة', 'برلمان', 'وزير', 'رئيس', 'انتخابات', 'حزب']
        economic_keywords = ['اقتصاد', 'تجارة', 'استثمار', 'بنك', 'نفط', 'غاز', 'دولار', 'دينار']
        security_keywords = ['أمن', 'شرطة', 'جيش', 'إرهاب', 'عملية', 'اعتقال', 'قتل', 'انفجار']
        health_keywords = ['صحة', 'مستشفى', 'طبيب', 'مرض', 'علاج', 'دواء', 'وباء']
        
        for keyword in political_keywords:
            if keyword in text:
                return 'سياسة'
        
        for keyword in economic_keywords:
            if keyword in text:
                return 'اقتصاد'
        
        for keyword in security_keywords:
            if keyword in text:
                return 'أمن'
        
        for keyword in health_keywords:
            if keyword in text:
                return 'صحة'
        
        return 'عام'
    
    def scrape_rss_feed(self, url: str, source_name: str) -> List[Dict]:
        """جمع الأخبار من RSS feed"""
        news_list = []
        try:
            logger.info(f"🔄 جمع الأخبار من {source_name}...")
            
            feed = feedparser.parse(url)
            
            for entry in feed.entries:
                try:
                    title = entry.get('title', '').strip()
                    content = entry.get('summary', entry.get('description', '')).strip()
                    link = entry.get('link', '')
                    
                    # فحص إذا كان الخبر عراقي
                    if not self.is_iraqi_news(title, content):
                        continue
                    
                    # تحويل التاريخ
                    published_date = datetime.now(self.iraq_tz)
                    if hasattr(entry, 'published_parsed') and entry.published_parsed:
                        published_date = datetime(*entry.published_parsed[:6])
                        published_date = self.iraq_tz.localize(published_date)
                    
                    # قبول الأخبار من آخر 24 ساعة بدلاً من اليوم فقط
                    now = datetime.now(self.iraq_tz)
                    time_diff = now - published_date
                    if time_diff.days > 1:
                        continue
                    
                    news_item = {
                        'title': title,
                        'content': content,
                        'summary': content[:200] + '...' if len(content) > 200 else content,
                        'url': link,
                        'source': source_name,
                        'category': self.categorize_news(title, content),
                        'published_date': published_date,
                        'is_iraqi': True,
                        'keywords': ', '.join([kw for kw in self.iraqi_keywords if kw in f"{title} {content}".lower()])
                    }
                    
                    news_list.append(news_item)
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة خبر من {source_name}: {str(e)}")
                    continue
            
            logger.info(f"📰 {source_name}: تم العثور على {len(news_list)} خبر عراقي")
            
        except Exception as e:
            logger.error(f"❌ خطأ في جمع الأخبار من {source_name}: {str(e)}")
        
        return news_list
    
    def scrape_website(self, url: str, source_name: str, selectors: Dict) -> List[Dict]:
        """جمع الأخبار من موقع ويب"""
        news_list = []
        try:
            logger.info(f"🔄 جمع الأخبار من {source_name}...")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن عناصر الأخبار
            news_elements = soup.select(selectors.get('container', '.news-item'))
            
            for element in news_elements[:20]:  # أخذ أول 20 خبر
                try:
                    title_elem = element.select_one(selectors.get('title', 'h2, h3, .title'))
                    title = title_elem.get_text().strip() if title_elem else ''
                    
                    content_elem = element.select_one(selectors.get('content', 'p, .content, .summary'))
                    content = content_elem.get_text().strip() if content_elem else ''
                    
                    link_elem = element.select_one(selectors.get('link', 'a'))
                    link = link_elem.get('href', '') if link_elem else ''
                    
                    if not title:
                        continue
                    
                    # فحص إذا كان الخبر عراقي
                    if not self.is_iraqi_news(title, content):
                        continue
                    
                    # إصلاح الرابط إذا كان نسبي
                    if link and not link.startswith('http'):
                        from urllib.parse import urljoin
                        link = urljoin(url, link)
                    
                    news_item = {
                        'title': title,
                        'content': content,
                        'summary': content[:200] + '...' if len(content) > 200 else content,
                        'url': link,
                        'source': source_name,
                        'category': self.categorize_news(title, content),
                        'published_date': datetime.now(self.iraq_tz),
                        'is_iraqi': True,
                        'keywords': ', '.join([kw for kw in self.iraqi_keywords if kw in f"{title} {content}".lower()])
                    }
                    
                    news_list.append(news_item)
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة خبر من {source_name}: {str(e)}")
                    continue
            
            logger.info(f"📰 {source_name}: تم العثور على {len(news_list)} خبر عراقي")
            
        except Exception as e:
            logger.error(f"❌ خطأ في جمع الأخبار من {source_name}: {str(e)}")
        
        return news_list
    


    def scrape_all_sources(self) -> Dict:
        """جمع الأخبار من جميع المصادر"""
        total_saved = 0
        errors = []

        # مصادر الأخبار العراقية
        iraqi_sources = [
            {
                'url': 'https://alforatnews.iq/news/iraq',
                'name': 'الفرات نيوز',
                'type': 'website'
            },
            {
                'url': 'https://shafaq.com/ar',
                'name': 'شفق نيوز',
                'type': 'website'
            },
            {
                'url': 'https://www.alsumaria.tv/iraq-news/48/%D9%85%D8%AD%D9%84%D9%8A%D8%A7%D8%AA',
                'name': 'السومرية نيوز',
                'type': 'website'
            },
            {
                'url': 'https://aljeebal.com/',
                'name': 'الجبال نيوز',
                'type': 'website'
            },
            {
                'url': 'https://www.al-mirbad.com/Home',
                'name': 'المربد',
                'type': 'website'
            },
            {
                'url': 'https://miliq.news/',
                'name': 'ميل نيوز',
                'type': 'website'
            },
            {
                'url': 'https://www.alrasheedmedia.com/',
                'name': 'قناة الرشيد',
                'type': 'website'
            },
            {
                'url': 'https://title.news/local/',
                'name': 'تايتل',
                'type': 'website'
            },
            {
                'url': 'https://www.iqiraq.news/lastnews',
                'name': 'IQ News',
                'type': 'website'
            },
            {
                'url': 'https://non14.net/local',
                'name': 'نون الخبرية',
                'type': 'website'
            },
            {
                'url': 'https://www.almaalomah.me/',
                'name': 'وكالة المعلومة',
                'type': 'website'
            },
            {
                'url': 'https://www.iraqakhbar.com/',
                'name': 'وكالة أخبار العراق',
                'type': 'website'
            },
            {
                'url': 'https://kalimaiq.com/news/1',
                'name': 'كلمة',
                'type': 'website'
            },
            {
                'url': 'https://alfallujah.tv/category/news/',
                'name': 'قناة الفلوجة',
                'type': 'website'
            },
            {
                'url': 'https://altaghier.tv/archives/category/%d8%a7%d8%ae%d8%a8%d8%a7%d8%b1/',
                'name': 'قناة التغيير',
                'type': 'website'
            },
            {
                'url': 'https://almadapaper.net/category/%d9%85%d8%ad%d9%84%d9%8a%d8%a7%d8%aa/',
                'name': 'صحيفة المدى',
                'type': 'website'
            },
            {
                'url': 'https://www.azzaman.com/',
                'name': 'صحيفة الزمان',
                'type': 'website'
            },
            {
                'url': 'https://utviraq.net/news/',
                'name': 'UTV',
                'type': 'website'
            },
            {
                'url': 'https://almasra.iq/category/%d8%a7%d9%84%d8%b9%d8%b1%d8%a7%d9%82/',
                'name': 'المسرى',
                'type': 'website'
            },
            {
                'url': 'https://www.addustour.com/',
                'name': 'صحيفة الدستور',
                'type': 'website'
            },
            {
                'url': 'https://www.newsabah.com/',
                'name': 'صحيفة الصباح الجديد',
                'type': 'website'
            },
            {
                'url': 'https://www.nasnews.com/',
                'name': 'ناس نيوز',
                'type': 'website'
            },
            {
                'url': 'https://baghdadtoday.news/lastnews',
                'name': 'بغداد اليوم',
                'type': 'website'
            },
            {
                'url': 'https://kitabat.com/',
                'name': 'كتابات في الميزان',
                'type': 'website'
            },
            {
                'url': 'https://almasalah.com/',
                'name': 'المسلة',
                'type': 'website'
            },
            {
                'url': 'https://www.alrafidain.news/News/Category/1/%D8%A7%D9%84%D8%B9%D8%B1%D8%A7%D9%82',
                'name': 'موسوعة الرافدين',
                'type': 'website'
            },
            {
                'url': 'https://today-agency.net/News/8/%D9%85%D8%AD%D9%84%D9%8A',
                'name': 'وكالة اليوم',
                'type': 'website'
            },
            {
                'url': 'https://alssaa.com/posts/all',
                'name': 'شبكة الساعة',
                'type': 'website'
            },
            {
                'url': 'https://aljeebal.com/posts',
                'name': 'الجبال',
                'type': 'website'
            },
            {
                'url': 'https://alawla.tv/local/',
                'name': 'قناة الاولى',
                'type': 'website'
            },
            {
                'url': 'https://nasiriyah.org/ar/post/category/allnews/',
                'name': 'شبكة اخبار الناصرية',
                'type': 'website'
            },
            {
                'url': 'https://observeriraq.net/category/%d8%a7%d9%84%d8%b9%d8%b1%d8%a7%d9%82/',
                'name': 'عراق اوبزيرفر',
                'type': 'website'
            },
            {
                'url': 'https://www.nrttv.com/ar/Babetekan.aspx?MapID=3',
                'name': 'NRT عربية',
                'type': 'website'
            }
        ]

        # جمع من المصادر العراقية (أول 5 مصادر لتجنب الحمل الزائد)
        for source in iraqi_sources[:5]:
            try:
                if source['type'] == 'rss':
                    news_list = self.scrape_rss_feed(source['url'], source['name'])
                else:
                    # استخدام selectors عامة للمواقع العراقية
                    selectors = {
                        'container': 'article, .news-item, .post, .entry, .article',
                        'title': 'h1, h2, h3, .title, .headline, .post-title',
                        'content': 'p, .content, .summary, .excerpt, .description',
                        'link': 'a'
                    }
                    news_list = self.scrape_website(source['url'], source['name'], selectors)

                saved_count = 0
                for news in news_list:
                    if self.db.save_news(news):
                        saved_count += 1

                total_saved += saved_count
                logger.info(f"✅ {source['name']}: تم حفظ {saved_count} خبر")

            except Exception as e:
                error_msg = f"خطأ في {source['name']}: {str(e)}"
                errors.append(error_msg)
                logger.error(f"❌ {error_msg}")

            time.sleep(3)  # توقف أطول بين المصادر لتجنب الحظر

        # لا نضيف أخبار افتراضية - فقط الأخبار الحقيقية من المصادر

        logger.info(f"🎉 انتهت عملية جمع الأخبار: {total_saved} خبر محفوظ، {len(errors)} خطأ")

        return {
            'total_saved': total_saved,
            'errors': errors,
            'timestamp': datetime.now(self.iraq_tz).isoformat()
        }
