#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة المصادر العراقية إلى قاعدة البيانات
"""

import json
from database import Database

def add_iraqi_sources():
    """إضافة جميع المصادر العراقية"""
    
    # إنشاء اتصال بقاعدة البيانات
    db = Database()
    
    # قائمة المصادر العراقية
    iraqi_sources = [
        {
            'name': 'الفرات نيوز',
            'url': 'https://alforatnews.iq/news/iraq',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post, .entry',
                'title': 'h1, h2, h3, .title, .headline',
                'content': 'p, .content, .summary, .excerpt',
                'link': 'a'
            }),
            'description': 'موقع الفرات نيوز للأخبار العراقية'
        },
        {
            'name': 'شفق نيوز',
            'url': 'https://shafaq.com/ar',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع شفق نيوز للأخبار العراقية'
        },
        {
            'name': 'السومرية نيوز',
            'url': 'https://www.alsumaria.tv/iraq-news/48/%D9%85%D8%AD%D9%84%D9%8A%D8%A7%D8%AA',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع السومرية نيوز للأخبار المحلية العراقية'
        },
        {
            'name': 'الجبال نيوز',
            'url': 'https://aljeebal.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع الجبال نيوز للأخبار العراقية'
        },
        {
            'name': 'المربد',
            'url': 'https://www.al-mirbad.com/Home',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع المربد للأخبار العراقية'
        },
        {
            'name': 'ميل نيوز',
            'url': 'https://miliq.news/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع ميل نيوز للأخبار العراقية'
        },
        {
            'name': 'قناة الرشيد',
            'url': 'https://www.alrasheedmedia.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع قناة الرشيد الإعلامية'
        },
        {
            'name': 'تايتل',
            'url': 'https://title.news/local/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع تايتل للأخبار المحلية العراقية'
        },
        {
            'name': 'IQ News',
            'url': 'https://www.iqiraq.news/lastnews',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع IQ News للأخبار العراقية'
        },
        {
            'name': 'نون الخبرية',
            'url': 'https://non14.net/local',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع نون الخبرية للأخبار المحلية'
        },
        {
            'name': 'وكالة المعلومة',
            'url': 'https://www.almaalomah.me/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'وكالة المعلومة للأنباء العراقية'
        },
        {
            'name': 'وكالة نون الخبرية',
            'url': 'https://www.non14.net/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'وكالة نون الخبرية للأنباء'
        },
        {
            'name': 'وكالة أخبار العراق',
            'url': 'https://www.iraqakhbar.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'وكالة أخبار العراق'
        },
        {
            'name': 'كلمة',
            'url': 'https://kalimaiq.com/news/1',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع كلمة للأخبار العراقية'
        },
        {
            'name': 'قناة الفلوجة',
            'url': 'https://alfallujah.tv/category/news/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع قناة الفلوجة الفضائية'
        },
        {
            'name': 'قناة التغيير',
            'url': 'https://altaghier.tv/archives/category/%d8%a7%d8%ae%d8%a8%d8%a7%d8%b1/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع قناة التغيير للأخبار'
        },
        {
            'name': 'صحيفة المدى',
            'url': 'https://almadapaper.net/category/%d9%85%d8%ad%d9%84%d9%8a%d8%a7%d8%aa/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'صحيفة المدى العراقية - الأخبار المحلية'
        },
        {
            'name': 'صحيفة الزمان',
            'url': 'https://www.azzaman.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'صحيفة الزمان العراقية'
        },
        {
            'name': 'UTV',
            'url': 'https://utviraq.net/news/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'قناة UTV العراقية للأخبار'
        },
        {
            'name': 'المسرى',
            'url': 'https://almasra.iq/category/%d8%a7%d9%84%d8%b9%d8%b1%d8%a7%d9%82/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع المسرى للأخبار العراقية'
        },
        {
            'name': 'صحيفة الدستور',
            'url': 'https://www.addustour.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'صحيفة الدستور العراقية'
        },
        {
            'name': 'المعلومة',
            'url': 'https://almaalomah.me/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع المعلومة للأخبار العراقية'
        },
        {
            'name': 'قناة الرابعة',
            'url': 'https://alrabiaa.tv/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع قناة الرابعة العراقية'
        },
        {
            'name': 'صحيفة الصباح الجديد',
            'url': 'https://www.newsabah.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'صحيفة الصباح الجديد العراقية'
        },
        {
            'name': 'ناس نيوز',
            'url': 'https://www.nasnews.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع ناس نيوز للأخبار العراقية'
        },
        {
            'name': 'بغداد اليوم',
            'url': 'https://baghdadtoday.news/lastnews',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع بغداد اليوم للأخبار العراقية'
        },
        {
            'name': 'كتابات في الميزان',
            'url': 'https://kitabat.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع كتابات في الميزان'
        },
        {
            'name': 'المسلة',
            'url': 'https://almasalah.com/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع المسلة للأخبار والتحليلات'
        },
        {
            'name': 'موسوعة الرافدين',
            'url': 'https://www.alrafidain.news/News/Category/1/%D8%A7%D9%84%D8%B9%D8%B1%D8%A7%D9%82',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موسوعة الرافدين للأخبار العراقية'
        },
        {
            'name': 'وكالة اليوم',
            'url': 'https://today-agency.net/News/8/%D9%85%D8%AD%D9%84%D9%8A',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'وكالة اليوم للأنباء المحلية'
        },
        {
            'name': 'شبكة الساعة',
            'url': 'https://alssaa.com/posts/all',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'شبكة الساعة الإخبارية'
        },
        {
            'name': 'الجبال',
            'url': 'https://aljeebal.com/posts',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع الجبال للأخبار'
        },
        {
            'name': 'قناة الاولى',
            'url': 'https://alawla.tv/local/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'قناة الاولى للأخبار المحلية'
        },
        {
            'name': 'شبكة اخبار الناصرية',
            'url': 'https://nasiriyah.org/ar/post/category/allnews/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'شبكة اخبار الناصرية'
        },
        {
            'name': 'عراق اوبزيرفر',
            'url': 'https://observeriraq.net/category/%d8%a7%d9%84%d8%b9%d8%b1%d8%a7%d9%82/',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'موقع عراق اوبزيرفر'
        },
        {
            'name': 'NRT عربية',
            'url': 'https://www.nrttv.com/ar/Babetekan.aspx?MapID=3',
            'type': 'website',
            'is_active': True,
            'selectors': json.dumps({
                'container': 'article, .news-item, .post',
                'title': 'h1, h2, h3, .title',
                'content': 'p, .content, .summary',
                'link': 'a'
            }),
            'description': 'قناة NRT عربية'
        }
    ]
    
    # إضافة المصادر
    added_count = 0
    for source in iraqi_sources:
        try:
            if db.add_source(source):
                added_count += 1
                print(f"✅ تم إضافة: {source['name']}")
            else:
                print(f"⚠️ فشل في إضافة: {source['name']}")
        except Exception as e:
            print(f"❌ خطأ في إضافة {source['name']}: {str(e)}")
    
    print(f"\n🎉 تم إضافة {added_count} مصدر من أصل {len(iraqi_sources)} مصدر")
    
    return added_count

if __name__ == '__main__':
    add_iraqi_sources()
