#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالجة مشاكل الشرقية ووكالة الأنباء العراقية
"""

import requests
import json
import time
from database import Database
from bs4 import BeautifulSoup
import feedparser

class SharqiyaINAFixer:
    def __init__(self):
        """تهيئة مصحح المصدرين"""
        self.db = Database()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache'
        })
    
    def test_multiple_urls(self, base_name, urls_to_test):
        """اختبار عدة روابط للمصدر الواحد"""
        print(f"\n🔍 اختبار روابط متعددة لـ {base_name}:")
        
        working_urls = []
        
        for i, url in enumerate(urls_to_test, 1):
            try:
                print(f"  {i}. اختبار: {url}")
                
                response = self.session.get(url, timeout=15, allow_redirects=True)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    
                    if 'html' in content_type:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        title = soup.find('title')
                        
                        if title and title.get_text().strip():
                            # فحص وجود محتوى إخباري
                            news_elements = soup.select('article, .news, .post, .entry, h1, h2, h3')
                            
                            working_urls.append({
                                'url': url,
                                'status': 'يعمل',
                                'title': title.get_text().strip()[:100],
                                'news_count': len(news_elements),
                                'final_url': response.url
                            })
                            
                            print(f"    ✅ يعمل - العنوان: {title.get_text().strip()[:50]}...")
                            print(f"    📊 عناصر إخبارية: {len(news_elements)}")
                            
                        else:
                            print(f"    ❌ HTML غير صحيح")
                    else:
                        print(f"    ❌ ليس HTML: {content_type}")
                else:
                    print(f"    ❌ HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"    ❌ انتهت مهلة الاتصال")
            except requests.exceptions.ConnectionError:
                print(f"    ❌ خطأ في الاتصال")
            except requests.exceptions.SSLError:
                print(f"    ❌ خطأ SSL")
            except Exception as e:
                print(f"    ❌ خطأ: {str(e)}")
            
            time.sleep(2)  # توقف بين الاختبارات
        
        return working_urls
    
    def fix_sharqiya(self):
        """معالجة مشاكل قناة الشرقية"""
        print("🔧 معالجة مشاكل قناة الشرقية...")
        
        # روابط مختلفة للشرقية للاختبار
        sharqiya_urls = [
            'https://alsharqiya.com/ar',
            'https://www.alsharqiya.com/ar',
            'https://alsharqiya.com/',
            'https://www.alsharqiya.com/',
            'https://alsharqiya.tv/',
            'https://www.alsharqiya.tv/',
            'https://alsharqiya.net/',
            'https://www.alsharqiya.net/'
        ]
        
        working_urls = self.test_multiple_urls("قناة الشرقية", sharqiya_urls)
        
        if working_urls:
            best_url = working_urls[0]  # أخذ أول رابط يعمل
            
            # محددات CSS محسنة للشرقية
            sharqiya_selectors = {
                'container': '.news-item, .post, article, .entry, .news-card, .item, .story, .content-item',
                'title': 'h1, h2, h3, .title, .news-title, .post-title, .card-title, .item-title, .story-title',
                'content': '.content, .summary, .excerpt, .description, p, .card-text, .item-content, .story-content',
                'link': 'a'
            }
            
            # إضافة المصدر
            source_data = {
                'name': 'قناة الشرقية',
                'url': best_url['url'],
                'type': 'website',
                'is_active': True,
                'selectors': json.dumps(sharqiya_selectors, ensure_ascii=False),
                'description': f'قناة الشرقية الإخبارية - {best_url["title"]}'
            }
            
            if self.db.add_source(source_data):
                print(f"✅ تم إضافة قناة الشرقية: {best_url['url']}")
                return True
            else:
                print("⚠️ قناة الشرقية موجودة مسبقاً أو فشل في الإضافة")
                return False
        else:
            print("❌ لم يتم العثور على رابط يعمل لقناة الشرقية")
            return False
    
    def fix_ina(self):
        """معالجة مشاكل وكالة الأنباء العراقية"""
        print("\n🔧 معالجة مشاكل وكالة الأنباء العراقية...")
        
        # روابط مختلفة لوكالة الأنباء العراقية
        ina_urls = [
            'https://ina.iq/',
            'https://www.ina.iq/',
            'https://ina.iq/eng/',
            'https://ina.iq/ar/',
            'https://ina.iq/news/',
            'https://ina.iq/ar/news/',
            'http://ina.iq/',
            'http://www.ina.iq/'
        ]
        
        working_urls = self.test_multiple_urls("وكالة الأنباء العراقية", ina_urls)
        
        if working_urls:
            best_url = working_urls[0]  # أخذ أول رابط يعمل
            
            # محددات CSS محسنة لوكالة الأنباء العراقية
            ina_selectors = {
                'container': '.news-item, .post, article, .entry, .news-card, .item, .story, .content-item, .news-box',
                'title': 'h1, h2, h3, .title, .news-title, .post-title, .card-title, .item-title, .story-title, .headline',
                'content': '.content, .summary, .excerpt, .description, p, .card-text, .item-content, .story-content, .news-content',
                'link': 'a'
            }
            
            # إضافة المصدر
            source_data = {
                'name': 'وكالة الأنباء العراقية',
                'url': best_url['url'],
                'type': 'website',
                'is_active': True,
                'selectors': json.dumps(ina_selectors, ensure_ascii=False),
                'description': f'وكالة الأنباء العراقية الرسمية - {best_url["title"]}'
            }
            
            if self.db.add_source(source_data):
                print(f"✅ تم إضافة وكالة الأنباء العراقية: {best_url['url']}")
                return True
            else:
                print("⚠️ وكالة الأنباء العراقية موجودة مسبقاً أو فشل في الإضافة")
                return False
        else:
            print("❌ لم يتم العثور على رابط يعمل لوكالة الأنباء العراقية")
            return False
    
    def test_rss_alternatives(self):
        """اختبار بدائل RSS للمصدرين"""
        print("\n📡 اختبار بدائل RSS...")
        
        rss_urls_to_test = [
            {
                'name': 'وكالة الأنباء العراقية RSS',
                'urls': [
                    'https://ina.iq/rss',
                    'https://ina.iq/feed',
                    'https://ina.iq/rss.xml',
                    'https://ina.iq/ar/rss',
                    'https://ina.iq/ar/feed'
                ]
            },
            {
                'name': 'قناة الشرقية RSS',
                'urls': [
                    'https://alsharqiya.com/rss',
                    'https://alsharqiya.com/feed',
                    'https://alsharqiya.com/rss.xml',
                    'https://alsharqiya.com/ar/rss',
                    'https://alsharqiya.com/ar/feed'
                ]
            }
        ]
        
        added_rss = 0
        
        for rss_source in rss_urls_to_test:
            print(f"\n🔍 اختبار RSS لـ {rss_source['name']}:")
            
            for url in rss_source['urls']:
                try:
                    print(f"  📡 اختبار: {url}")
                    
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '').lower()
                        
                        if 'xml' in content_type or 'rss' in content_type:
                            feed = feedparser.parse(response.content)
                            
                            if feed.bozo == 0 and len(feed.entries) > 0:
                                print(f"    ✅ RSS صحيح - {len(feed.entries)} عنصر")
                                
                                # إضافة مصدر RSS
                                rss_data = {
                                    'name': rss_source['name'],
                                    'url': url,
                                    'type': 'rss',
                                    'is_active': True,
                                    'selectors': '',
                                    'description': f'تغذية RSS لـ {rss_source["name"]}'
                                }
                                
                                if self.db.add_source(rss_data):
                                    added_rss += 1
                                    print(f"    ✅ تم إضافة RSS: {rss_source['name']}")
                                    break  # توقف عند أول RSS يعمل
                                else:
                                    print(f"    ⚠️ RSS موجود مسبقاً")
                            else:
                                print(f"    ❌ RSS غير صحيح")
                        else:
                            print(f"    ❌ ليس XML/RSS")
                    else:
                        print(f"    ❌ HTTP {response.status_code}")
                        
                except Exception as e:
                    print(f"    ❌ خطأ: {str(e)}")
                
                time.sleep(1)
        
        return added_rss
    
    def add_alternative_sources(self):
        """إضافة مصادر بديلة موثوقة"""
        print("\n➕ إضافة مصادر بديلة موثوقة...")
        
        alternative_sources = [
            {
                'name': 'العراقية الإخبارية',
                'url': 'https://iraqinews.com/',
                'type': 'website',
                'description': 'قناة العراقية الإخبارية'
            },
            {
                'name': 'الحرة عراق',
                'url': 'https://www.alhurra.com/iraq',
                'type': 'website',
                'description': 'قناة الحرة - أخبار العراق'
            },
            {
                'name': 'راديو سوا عراق',
                'url': 'https://www.radiosawa.com/iraq',
                'type': 'website',
                'description': 'راديو سوا - أخبار العراق'
            },
            {
                'name': 'العربية نت عراق',
                'url': 'https://www.alarabiya.net/arab-and-world/iraq',
                'type': 'website',
                'description': 'العربية نت - أخبار العراق'
            }
        ]
        
        added_alternatives = 0
        
        for source in alternative_sources:
            try:
                print(f"🔍 اختبار: {source['name']}")
                
                response = self.session.get(source['url'], timeout=15)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    title = soup.find('title')
                    
                    if title and title.get_text().strip():
                        # محددات عامة
                        selectors = {
                            'container': 'article, .article, .news-item, .post, .entry, .news, .item, .story',
                            'title': 'h1, h2, h3, .title, .headline, .news-title, .post-title',
                            'content': '.content, .summary, .excerpt, p, .news-content, .post-content',
                            'link': 'a'
                        }
                        
                        source_data = {
                            'name': source['name'],
                            'url': source['url'],
                            'type': source['type'],
                            'is_active': True,
                            'selectors': json.dumps(selectors, ensure_ascii=False),
                            'description': source['description']
                        }
                        
                        if self.db.add_source(source_data):
                            added_alternatives += 1
                            print(f"✅ تم إضافة: {source['name']}")
                        else:
                            print(f"⚠️ موجود مسبقاً: {source['name']}")
                    else:
                        print(f"❌ HTML غير صحيح: {source['name']}")
                else:
                    print(f"❌ HTTP {response.status_code}: {source['name']}")
                    
            except Exception as e:
                print(f"❌ خطأ في {source['name']}: {str(e)}")
            
            time.sleep(2)
        
        return added_alternatives
    
    def run_fix(self):
        """تشغيل عملية الإصلاح الكاملة"""
        print("🚀 بدء معالجة مشاكل الشرقية ووكالة الأنباء العراقية...")
        
        results = {
            'sharqiya_fixed': False,
            'ina_fixed': False,
            'rss_added': 0,
            'alternatives_added': 0
        }
        
        # إصلاح الشرقية
        results['sharqiya_fixed'] = self.fix_sharqiya()
        
        # إصلاح وكالة الأنباء العراقية
        results['ina_fixed'] = self.fix_ina()
        
        # اختبار بدائل RSS
        results['rss_added'] = self.test_rss_alternatives()
        
        # إضافة مصادر بديلة
        results['alternatives_added'] = self.add_alternative_sources()
        
        # تقرير النتائج
        print(f"\n🎉 انتهت عملية الإصلاح:")
        print(f"📺 قناة الشرقية: {'✅ تم الإصلاح' if results['sharqiya_fixed'] else '❌ لم يتم الإصلاح'}")
        print(f"📰 وكالة الأنباء العراقية: {'✅ تم الإصلاح' if results['ina_fixed'] else '❌ لم يتم الإصلاح'}")
        print(f"📡 مصادر RSS مضافة: {results['rss_added']}")
        print(f"➕ مصادر بديلة مضافة: {results['alternatives_added']}")
        
        return results

if __name__ == '__main__':
    fixer = SharqiyaINAFixer()
    fixer.run_fix()
