const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = path.join(__dirname, '..', 'data', 'iraqi_news.db');

let db;

// Initialize database connection
function initDatabase() {
  return new Promise((resolve, reject) => {
    // Create data directory if it doesn't exist
    const fs = require('fs');
    const dataDir = path.dirname(DB_PATH);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
        reject(err);
      } else {
        console.log('تم الاتصال بقاعدة البيانات SQLite');
        createTables()
          .then(resolve)
          .catch(reject);
      }
    });
  });
}

// Create necessary tables
function createTables() {
  return new Promise((resolve, reject) => {
    const createNewsTable = `
      CREATE TABLE IF NOT EXISTS news (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT,
        summary TEXT,
        url TEXT UNIQUE NOT NULL,
        source TEXT NOT NULL,
        category TEXT,
        author TEXT,
        published_date DATETIME,
        scraped_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        image_url TEXT,
        tags TEXT,
        views INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT 1
      )
    `;

    const createSourcesTable = `
      CREATE TABLE IF NOT EXISTS sources (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        url TEXT NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        last_scraped DATETIME,
        scrape_interval INTEGER DEFAULT 30,
        created_date DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const createCategoriesTable = `
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        name_ar TEXT NOT NULL,
        description TEXT,
        created_date DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    db.serialize(() => {
      db.run(createNewsTable, (err) => {
        if (err) {
          console.error('خطأ في إنشاء جدول الأخبار:', err.message);
          reject(err);
          return;
        }
      });

      db.run(createSourcesTable, (err) => {
        if (err) {
          console.error('خطأ في إنشاء جدول المصادر:', err.message);
          reject(err);
          return;
        }
      });

      db.run(createCategoriesTable, (err) => {
        if (err) {
          console.error('خطأ في إنشاء جدول الفئات:', err.message);
          reject(err);
          return;
        }
        
        // Insert default sources and categories
        insertDefaultData()
          .then(resolve)
          .catch(reject);
      });
    });
  });
}

// Insert default sources and categories
function insertDefaultData() {
  return new Promise((resolve, reject) => {
    const sources = [
      { name: 'shafaq', url: 'https://shafaq.com', scrape_interval: 15 },
      { name: 'hathalyoum', url: 'https://hathalyoum.net', scrape_interval: 20 },
      { name: 'media964', url: 'https://964media.com', scrape_interval: 25 },
      { name: 'alsumaria', url: 'https://www.alsumaria.tv', scrape_interval: 18 }
    ];

    const categories = [
      { name: 'politics', name_ar: 'سياسة', description: 'الأخبار السياسية' },
      { name: 'economy', name_ar: 'اقتصاد', description: 'الأخبار الاقتصادية' },
      { name: 'security', name_ar: 'أمن', description: 'الأخبار الأمنية' },
      { name: 'sports', name_ar: 'رياضة', description: 'الأخبار الرياضية' },
      { name: 'culture', name_ar: 'ثقافة', description: 'الأخبار الثقافية' },
      { name: 'technology', name_ar: 'تكنولوجيا', description: 'أخبار التكنولوجيا' },
      { name: 'health', name_ar: 'صحة', description: 'الأخبار الصحية' },
      { name: 'general', name_ar: 'عام', description: 'أخبار عامة' }
    ];

    let completed = 0;
    const total = sources.length + categories.length;

    // Insert sources
    sources.forEach(source => {
      db.run(
        'INSERT OR IGNORE INTO sources (name, url, scrape_interval) VALUES (?, ?, ?)',
        [source.name, source.url, source.scrape_interval],
        (err) => {
          if (err) console.error('خطأ في إدراج المصدر:', err.message);
          completed++;
          if (completed === total) resolve();
        }
      );
    });

    // Insert categories
    categories.forEach(category => {
      db.run(
        'INSERT OR IGNORE INTO categories (name, name_ar, description) VALUES (?, ?, ?)',
        [category.name, category.name_ar, category.description],
        (err) => {
          if (err) console.error('خطأ في إدراج الفئة:', err.message);
          completed++;
          if (completed === total) resolve();
        }
      );
    });
  });
}

// Get database instance
function getDatabase() {
  return db;
}

// Close database connection
function closeDatabase() {
  return new Promise((resolve) => {
    if (db) {
      db.close((err) => {
        if (err) {
          console.error('خطأ في إغلاق قاعدة البيانات:', err.message);
        } else {
          console.log('تم إغلاق الاتصال بقاعدة البيانات');
        }
        resolve();
      });
    } else {
      resolve();
    }
  });
}

module.exports = {
  initDatabase,
  getDatabase,
  closeDatabase
};
