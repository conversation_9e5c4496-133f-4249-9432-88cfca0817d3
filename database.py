#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات لنظام أخبار العراق
"""

import sqlite3
import logging
from datetime import datetime
import pytz
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class Database:
    def __init__(self, db_path: str = 'iraqi_news.db'):
        """تهيئة قاعدة البيانات"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول الأخبار
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS news (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        content TEXT,
                        summary TEXT,
                        url TEXT UNIQUE,
                        source TEXT NOT NULL,
                        category TEXT DEFAULT 'عام',
                        published_date DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        is_iraqi BOOLEAN DEFAULT 1,
                        keywords TEXT
                    )
                ''')
                
                # فهرسة للبحث السريع
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_published_date ON news(published_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_source ON news(source)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_category ON news(category)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_is_iraqi ON news(is_iraqi)')
                
                conn.commit()
                logger.info("✅ تم تهيئة قاعدة البيانات بنجاح")
                
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة قاعدة البيانات: {str(e)}")
            raise
    
    def save_news(self, news_data: Dict) -> bool:
        """حفظ خبر جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR IGNORE INTO news 
                    (title, content, summary, url, source, category, published_date, is_iraqi, keywords)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    news_data.get('title', ''),
                    news_data.get('content', ''),
                    news_data.get('summary', ''),
                    news_data.get('url', ''),
                    news_data.get('source', ''),
                    news_data.get('category', 'عام'),
                    news_data.get('published_date'),
                    news_data.get('is_iraqi', True),
                    news_data.get('keywords', '')
                ))
                
                if cursor.rowcount > 0:
                    logger.info(f"✅ تم حفظ: {news_data.get('title', '')[:50]}...")
                    return True
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الخبر: {str(e)}")
            return False
    
    def get_today_news(self, limit: int = 50) -> List[Dict]:
        """جلب أخبار اليوم"""
        try:
            iraq_tz = pytz.timezone('Asia/Baghdad')
            today = datetime.now(iraq_tz).strftime('%Y-%m-%d')
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM news 
                    WHERE DATE(published_date) = ? AND is_iraqi = 1
                    ORDER BY published_date DESC 
                    LIMIT ?
                ''', (today, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب أخبار اليوم: {str(e)}")
            return []
    
    def get_all_news(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """جلب جميع الأخبار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM news 
                    WHERE is_iraqi = 1
                    ORDER BY published_date DESC 
                    LIMIT ? OFFSET ?
                ''', (limit, offset))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الأخبار: {str(e)}")
            return []
    
    def search_news(self, query: str, limit: int = 50) -> List[Dict]:
        """البحث في الأخبار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM news 
                    WHERE (title LIKE ? OR content LIKE ? OR keywords LIKE ?) 
                    AND is_iraqi = 1
                    ORDER BY published_date DESC 
                    LIMIT ?
                ''', (f'%{query}%', f'%{query}%', f'%{query}%', limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"❌ خطأ في البحث: {str(e)}")
            return []
    
    def get_news_by_category(self, category: str, limit: int = 50) -> List[Dict]:
        """جلب الأخبار حسب الفئة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM news 
                    WHERE category = ? AND is_iraqi = 1
                    ORDER BY published_date DESC 
                    LIMIT ?
                ''', (category, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب أخبار الفئة: {str(e)}")
            return []
    
    def get_stats(self) -> Dict:
        """جلب إحصائيات الأخبار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # إجمالي الأخبار
                cursor.execute('SELECT COUNT(*) FROM news WHERE is_iraqi = 1')
                total_news = cursor.fetchone()[0]
                
                # أخبار اليوم
                iraq_tz = pytz.timezone('Asia/Baghdad')
                today = datetime.now(iraq_tz).strftime('%Y-%m-%d')
                cursor.execute('SELECT COUNT(*) FROM news WHERE DATE(published_date) = ? AND is_iraqi = 1', (today,))
                today_news = cursor.fetchone()[0]
                
                # إحصائيات المصادر
                cursor.execute('''
                    SELECT source, COUNT(*) as count 
                    FROM news WHERE is_iraqi = 1 
                    GROUP BY source 
                    ORDER BY count DESC
                ''')
                sources = [{'source': row[0], 'count': row[1]} for row in cursor.fetchall()]
                
                # إحصائيات الفئات
                cursor.execute('''
                    SELECT category, COUNT(*) as count 
                    FROM news WHERE is_iraqi = 1 
                    GROUP BY category 
                    ORDER BY count DESC
                ''')
                categories = [{'category': row[0], 'count': row[1]} for row in cursor.fetchall()]
                
                return {
                    'total_news': total_news,
                    'today_news': today_news,
                    'sources': sources,
                    'categories': categories
                }
                
        except Exception as e:
            logger.error(f"❌ خطأ في جلب الإحصائيات: {str(e)}")
            return {'total_news': 0, 'today_news': 0, 'sources': [], 'categories': []}
    
    def clear_all_news(self) -> int:
        """حذف جميع الأخبار"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT COUNT(*) FROM news')
                count = cursor.fetchone()[0]
                
                cursor.execute('DELETE FROM news')
                cursor.execute('DELETE FROM sqlite_sequence WHERE name="news"')
                
                conn.commit()
                logger.info(f"🗑️ تم حذف {count} خبر من قاعدة البيانات")
                return count
                
        except Exception as e:
            logger.error(f"❌ خطأ في حذف الأخبار: {str(e)}")
            return 0
