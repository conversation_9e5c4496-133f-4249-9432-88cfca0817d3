@echo off
chcp 65001 >nul
color 0A
title 🇮🇶 نظام أخبار العراق - iNews Launcher

:MAIN_MENU
cls
echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██           🇮🇶 نظام أخبار العراق - iNews                ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.
echo 📋 اختر العملية المطلوبة:
echo.
echo    1️⃣  تشغيل النظام وفتح المتصفح
echo    2️⃣  تشغيل النظام فقط (بدون متصفح)
echo    3️⃣  فحص وتثبيت المتطلبات
echo    4️⃣  فتح المتصفح (إذا كان النظام يعمل)
echo    5️⃣  عرض معلومات النظام
echo    0️⃣  خروج
echo.
echo ████████████████████████████████████████████████████████████
echo.
set /p choice="🔢 اختر رقم العملية: "

if "%choice%"=="1" goto START_WITH_BROWSER
if "%choice%"=="2" goto START_ONLY
if "%choice%"=="3" goto CHECK_REQUIREMENTS
if "%choice%"=="4" goto OPEN_BROWSER
if "%choice%"=="5" goto SHOW_INFO
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:START_WITH_BROWSER
cls
echo.
echo 🚀 بدء تشغيل نظام أخبار العراق مع فتح المتصفح...
echo.
call :CHECK_PYTHON
call :CHECK_FILES
call :INSTALL_REQUIREMENTS
echo.
echo 🚀 بدء تشغيل الخادم مع فتح المتصفح التلقائي...
echo 📍 الرابط: http://localhost:5020
echo 🌐 المتصفح سيفتح تلقائياً عند جهوزية السيرفر
echo.
python start_inews_with_browser.py
goto END

:START_ONLY
cls
echo.
echo 🚀 بدء تشغيل نظام أخبار العراق...
echo.
call :CHECK_PYTHON
call :CHECK_FILES
call :INSTALL_REQUIREMENTS
echo.
echo 📍 الخادم سيعمل على: http://localhost:5020
echo 💡 يمكنك فتح المتصفح يدوياً والذهاب للرابط أعلاه
echo.
python app.py
goto END

:CHECK_REQUIREMENTS
cls
echo.
echo 🔍 فحص وتثبيت المتطلبات...
echo.
call :CHECK_PYTHON
call :CHECK_FILES
call :INSTALL_REQUIREMENTS
echo.
echo ✅ انتهى فحص المتطلبات
pause
goto MAIN_MENU

:OPEN_BROWSER
cls
echo.
echo 🌐 فتح المتصفح...
echo.
start http://localhost:5020
echo ✅ تم فتح المتصفح على: http://localhost:5020
echo.
pause
goto MAIN_MENU

:SHOW_INFO
cls
echo.
echo ████████████████████████████████████████████████████████████
echo ██                معلومات النظام                        ██
echo ████████████████████████████████████████████████████████████
echo.
echo 📋 اسم النظام: نظام أخبار العراق - iNews
echo 🌐 الرابط الرئيسي: http://localhost:5020
echo 📰 صفحة الأخبار: http://localhost:5020/news
echo 🔧 إدارة المصادر: http://localhost:5020/sources
echo ⚙️ لوحة الإدارة: http://localhost:5020/admin
echo.
echo 📊 المميزات:
echo    • جمع تلقائي للأخبار العراقية
echo    • استثناء أخبار إقليم كردستان
echo    • 23+ مصدر إخباري عراقي
echo    • تحديث كل دقيقة
echo    • واجهة عربية سهلة الاستخدام
echo.
echo 💻 المتطلبات:
echo    • Python 3.7+
echo    • Flask, Requests, BeautifulSoup4
echo    • اتصال بالإنترنت
echo.
pause
goto MAIN_MENU

:CHECK_PYTHON
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo ✅ Python مثبت
goto :eof

:CHECK_FILES
if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    pause
    exit /b 1
)
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)
echo ✅ جميع الملفات موجودة
goto :eof

:INSTALL_REQUIREMENTS
echo 🔍 فحص المتطلبات...
python -c "import flask, requests, beautifulsoup4, feedparser" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المتطلبات بنجاح
) else (
    echo ✅ جميع المتطلبات مثبتة
)
goto :eof

:END
echo.
echo 🛑 تم إيقاف نظام أخبار العراق
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo 👋 شكراً لاستخدام نظام أخبار العراق - iNews
echo.
timeout /t 2 /nobreak >nul
exit
