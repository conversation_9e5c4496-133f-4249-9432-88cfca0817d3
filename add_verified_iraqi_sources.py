#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة المصادر الإخبارية العراقية الموثوقة بعد فحص صلاحيتها
"""

import requests
import json
import time
from database import Database
from bs4 import BeautifulSoup
import feedparser

class IraqiSourcesManager:
    def __init__(self):
        """تهيئة مدير المصادر العراقية"""
        self.db = Database()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
    
    def get_iraqi_sources_list(self):
        """قائمة المصادر الإخبارية العراقية الموثوقة"""
        return [
            # المصادر الرئيسية المؤكدة
            {
                'name': 'وكالة الأنباء العراقية',
                'url': 'https://ina.iq/',
                'type': 'website',
                'description': 'وكالة الأنباء العراقية الرسمية',
                'priority': 1
            },
            {
                'name': 'شفق نيوز',
                'url': 'https://shafaq.com/ar',
                'type': 'website',
                'description': 'موقع شفق نيوز للأخبار العراقية',
                'priority': 1
            },
            {
                'name': 'السومرية نيوز',
                'url': 'https://www.alsumaria.tv/',
                'type': 'website',
                'description': 'قناة السومرية الإخبارية',
                'priority': 1
            },
            {
                'name': 'الفرات نيوز',
                'url': 'https://alforatnews.iq/',
                'type': 'website',
                'description': 'موقع الفرات نيوز للأخبار العراقية',
                'priority': 1
            },
            {
                'name': 'بغداد اليوم',
                'url': 'https://baghdadtoday.news/',
                'type': 'website',
                'description': 'موقع بغداد اليوم للأخبار العراقية',
                'priority': 1
            },
            {
                'name': 'وكالة المعلومة',
                'url': 'https://almaalomah.me/',
                'type': 'website',
                'description': 'وكالة المعلومة للأنباء العراقية',
                'priority': 1
            },
            {
                'name': 'كتابات في الميزان',
                'url': 'https://kitabat.com/',
                'type': 'website',
                'description': 'موقع كتابات في الميزان',
                'priority': 1
            },
            {
                'name': 'نون الخبرية',
                'url': 'https://non14.net/',
                'type': 'website',
                'description': 'وكالة نون الخبرية',
                'priority': 1
            },
            {
                'name': 'صحيفة المدى',
                'url': 'https://almadapaper.net/',
                'type': 'website',
                'description': 'صحيفة المدى العراقية',
                'priority': 1
            },
            {
                'name': 'المسلة',
                'url': 'https://almasalah.com/',
                'type': 'website',
                'description': 'موقع المسلة للأخبار والتحليلات',
                'priority': 1
            },
            {
                'name': 'صحيفة الزمان',
                'url': 'https://www.azzaman.com/',
                'type': 'website',
                'description': 'صحيفة الزمان العراقية',
                'priority': 1
            },
            {
                'name': 'شبكة أخبار الناصرية',
                'url': 'https://nasiriyah.org/ar/',
                'type': 'website',
                'description': 'شبكة أخبار الناصرية',
                'priority': 2
            },
            {
                'name': 'الشرقية نيوز',
                'url': 'https://alsharqiya.com/ar',
                'type': 'website',
                'description': 'قناة الشرقية الإخبارية',
                'priority': 1
            },
            {
                'name': 'شبكة الإعلام العراقي',
                'url': 'https://imn.iq/',
                'type': 'website',
                'description': 'شبكة الإعلام العراقي الرسمية',
                'priority': 1
            },
            {
                'name': 'صوت العراق',
                'url': 'http://www.sotaliraq.com/',
                'type': 'website',
                'description': 'موقع صوت العراق الإخباري',
                'priority': 2
            },
            {
                'name': 'شبكة أخبار العراق',
                'url': 'http://aliraqnews.com/',
                'type': 'website',
                'description': 'شبكة أخبار العراق',
                'priority': 2
            },
            {
                'name': 'باس نيوز',
                'url': 'https://www.basnews.com/ar/',
                'type': 'website',
                'description': 'وكالة باس نيوز الكردية',
                'priority': 2
            },
            {
                'name': 'أوين أونلاين',
                'url': 'https://www.awene.com/',
                'type': 'website',
                'description': 'موقع أوين الإخباري',
                'priority': 2
            },
            {
                'name': 'مانكيش نت',
                'url': 'https://mangish.net/',
                'type': 'website',
                'description': 'موقع مانكيش الإخباري',
                'priority': 3
            },
            {
                'name': 'لفين برس',
                'url': 'https://lvinpress.com/',
                'type': 'website',
                'description': 'مجلة لفين الكردية',
                'priority': 3
            },
            {
                'name': 'صحيفة خبات',
                'url': 'https://xebat.net/ku/',
                'type': 'website',
                'description': 'صحيفة خبات الكردية',
                'priority': 3
            },
            # مصادر RSS
            {
                'name': 'شفق نيوز RSS',
                'url': 'https://shafaq.com/rss',
                'type': 'rss',
                'description': 'تغذية RSS لموقع شفق نيوز',
                'priority': 1
            },
            {
                'name': 'السومرية RSS',
                'url': 'https://www.alsumaria.tv/rss',
                'type': 'rss',
                'description': 'تغذية RSS للسومرية',
                'priority': 1
            },
            {
                'name': 'كتابات RSS',
                'url': 'https://kitabat.com/feed',
                'type': 'rss',
                'description': 'تغذية RSS لكتابات في الميزان',
                'priority': 2
            }
        ]
    
    def check_source_availability(self, source):
        """فحص توفر وصلاحية المصدر"""
        try:
            print(f"🔍 فحص المصدر: {source['name']}")
            
            response = self.session.get(source['url'], timeout=15, allow_redirects=True)
            
            if response.status_code != 200:
                print(f"❌ {source['name']}: HTTP {response.status_code}")
                return False, f"HTTP {response.status_code}"
            
            # فحص نوع المحتوى
            content_type = response.headers.get('content-type', '').lower()
            
            if source['type'] == 'rss':
                # فحص RSS
                if 'xml' in content_type or 'rss' in content_type:
                    feed = feedparser.parse(response.content)
                    if feed.bozo == 0 and len(feed.entries) > 0:
                        print(f"✅ {source['name']}: RSS صحيح - {len(feed.entries)} عنصر")
                        return True, f"RSS صحيح - {len(feed.entries)} عنصر"
                    else:
                        print(f"❌ {source['name']}: RSS غير صحيح")
                        return False, "RSS غير صحيح"
                else:
                    print(f"❌ {source['name']}: ليس RSS")
                    return False, "ليس RSS"
            
            elif source['type'] == 'website':
                # فحص موقع الويب
                if 'html' in content_type:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    title = soup.find('title')
                    if title and title.get_text().strip():
                        # فحص وجود محتوى إخباري
                        news_indicators = soup.select('article, .news, .post, .entry, h1, h2, h3')
                        if len(news_indicators) > 5:
                            print(f"✅ {source['name']}: موقع ويب صحيح")
                            return True, f"موقع ويب صحيح - {len(news_indicators)} عنصر"
                        else:
                            print(f"⚠️ {source['name']}: محتوى قليل")
                            return True, "محتوى قليل لكن صالح"
                    else:
                        print(f"❌ {source['name']}: HTML غير صحيح")
                        return False, "HTML غير صحيح"
                else:
                    print(f"❌ {source['name']}: ليس HTML")
                    return False, "ليس HTML"
            
            return True, "تم الوصول بنجاح"
            
        except requests.exceptions.Timeout:
            print(f"❌ {source['name']}: انتهت مهلة الاتصال")
            return False, "انتهت مهلة الاتصال"
        except requests.exceptions.ConnectionError:
            print(f"❌ {source['name']}: خطأ في الاتصال")
            return False, "خطأ في الاتصال"
        except requests.exceptions.SSLError:
            print(f"❌ {source['name']}: خطأ SSL")
            return False, "خطأ SSL"
        except Exception as e:
            print(f"❌ {source['name']}: خطأ - {str(e)}")
            return False, f"خطأ: {str(e)}"
    
    def get_enhanced_selectors(self, source_name, url):
        """الحصول على محددات CSS محسنة للمصدر"""
        
        # محددات مخصصة للمواقع المعروفة
        custom_selectors = {
            'شفق نيوز': {
                'container': '.news-item, .post, article, .entry, .news-card',
                'title': 'h1, h2, h3, .post-title, .news-title, .title, .card-title',
                'content': '.post-content, .news-content, .content, .excerpt, p, .card-text',
                'link': 'a'
            },
            'السومرية نيوز': {
                'container': '.news-item, .article-item, .post, article, .item',
                'title': 'h1, h2, h3, .title, .news-title, .article-title, .item-title',
                'content': '.content, .summary, .excerpt, p, .item-content',
                'link': 'a'
            },
            'الفرات نيوز': {
                'container': '.news-box, .news-item, .post, article, .entry, .item',
                'title': 'h1, h2, h3, .news-title, .title, .headline, .item-title',
                'content': '.news-content, .content, .summary, p, .item-content',
                'link': 'a'
            },
            'بغداد اليوم': {
                'container': '.news-item, .post, article, .news-card, .item',
                'title': 'h1, h2, h3, .news-title, .card-title, .title, .item-title',
                'content': '.news-content, .card-text, .content, .summary, p, .item-content',
                'link': 'a'
            },
            'وكالة المعلومة': {
                'container': '.news-item, .post, article, .entry, .item',
                'title': 'h1, h2, h3, .news-title, .post-title, .title, .item-title',
                'content': '.news-content, .post-content, .content, .excerpt, p, .item-content',
                'link': 'a'
            }
        }
        
        # إذا كان هناك محددات مخصصة
        if source_name in custom_selectors:
            return custom_selectors[source_name]
        
        # محددات عامة محسنة
        return {
            'container': 'article, .article, .news-item, .post, .entry, .news, .item, .story, .card, .content-item, .news-box, .post-item, .news-card, .article-item',
            'title': 'h1, h2, h3, h4, .title, .headline, .post-title, .news-title, .article-title, .story-title, .entry-title, .item-title, .content-title',
            'content': 'p, .content, .summary, .excerpt, .description, .text, .body, .news-content, .post-content, .article-content, .story-content, .entry-content',
            'link': 'a, .link, .news-link, .post-link, .article-link, .story-link, .entry-link, .item-link'
        }
    
    def add_verified_sources(self):
        """إضافة المصادر بعد فحص صلاحيتها"""
        sources_list = self.get_iraqi_sources_list()
        
        # ترتيب المصادر حسب الأولوية
        sources_list.sort(key=lambda x: x['priority'])
        
        added_count = 0
        failed_count = 0
        
        print("🚀 بدء فحص وإضافة المصادر الإخبارية العراقية...")
        print(f"📊 إجمالي المصادر للفحص: {len(sources_list)}")
        
        for source in sources_list:
            try:
                # فحص صلاحية المصدر
                is_available, status_msg = self.check_source_availability(source)
                
                if is_available:
                    # إعداد بيانات المصدر
                    source_data = {
                        'name': source['name'],
                        'url': source['url'],
                        'type': source['type'],
                        'is_active': True,
                        'description': source['description']
                    }
                    
                    # إضافة محددات CSS للمواقع
                    if source['type'] == 'website':
                        selectors = self.get_enhanced_selectors(source['name'], source['url'])
                        source_data['selectors'] = json.dumps(selectors, ensure_ascii=False)
                    else:
                        source_data['selectors'] = ''
                    
                    # إضافة المصدر إلى قاعدة البيانات
                    if self.db.add_source(source_data):
                        added_count += 1
                        print(f"✅ تم إضافة: {source['name']}")
                    else:
                        print(f"⚠️ المصدر موجود مسبقاً: {source['name']}")
                else:
                    failed_count += 1
                    print(f"❌ فشل المصدر: {source['name']} - {status_msg}")
                
                # توقف قصير بين الفحوصات
                time.sleep(2)
                
            except Exception as e:
                failed_count += 1
                print(f"❌ خطأ في معالجة {source['name']}: {str(e)}")
        
        print(f"\n🎉 انتهى فحص وإضافة المصادر:")
        print(f"✅ تم إضافة: {added_count} مصدر")
        print(f"❌ فشل: {failed_count} مصدر")
        print(f"📊 إجمالي المصادر المفحوصة: {len(sources_list)}")
        
        return added_count, failed_count

if __name__ == '__main__':
    manager = IraqiSourcesManager()
    manager.add_verified_sources()
