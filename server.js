const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const newsRoutes = require('./routes/news');
const { initDatabase } = require('./config/database');
const { startScheduler } = require('./utils/scheduler');

const app = express();
const PORT = process.env.PORT || 5020;

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً'
  }
});
app.use('/api/', limiter);

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/news', newsRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Iraqi News API is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Root endpoint with HTML interface
app.get('/', (req, res) => {
  const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API الأخبار العراقية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .endpoints {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .endpoint {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .endpoint:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .endpoint-url {
            background: #2c3e50;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .endpoint-desc {
            color: #666;
            font-size: 0.95em;
        }
        .sources {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .source {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        .stats {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-top: 20px;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 5px;
            transition: transform 0.3s ease;
            font-weight: bold;
        }
        .btn:hover {
            transform: scale(1.05);
        }
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇮🇶 API الأخبار العراقية</h1>
            <p>الإصدار 1.0.0 - مصدرك الموثوق للأخبار العراقية</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>📡 نقاط النهاية المتاحة</h2>
                <div class="endpoints">
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news</div>
                        <div class="endpoint-desc">جلب جميع الأخبار مع إمكانية التصفية والترقيم</div>
                        <a href="/api/news?limit=5" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/latest</div>
                        <div class="endpoint-desc">أحدث الأخبار من جميع المصادر</div>
                        <a href="/api/news/latest" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/search/:query</div>
                        <div class="endpoint-desc">البحث في الأخبار باستخدام كلمات مفتاحية</div>
                        <a href="/api/news/search/العراق" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/category/:category</div>
                        <div class="endpoint-desc">أخبار حسب الفئة (سياسة، اقتصاد، رياضة، إلخ)</div>
                        <a href="/api/news/category/politics" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/source/:source</div>
                        <div class="endpoint-desc">أخبار من مصدر محدد</div>
                        <a href="/api/news/source/bbc-arabic" class="btn">جرب الآن</a>
                    </div>
                    <div class="endpoint">
                        <div class="endpoint-url">GET /api/news/stats</div>
                        <div class="endpoint-desc">إحصائيات مفصلة عن الأخبار والمصادر</div>
                        <a href="/api/news/stats" class="btn">جرب الآن</a>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📰 المصادر المدعومة</h2>
                <div class="sources">
                    <div class="source">BBC العربية</div>
                    <div class="source">سكاي نيوز العربية</div>
                    <div class="source">الأخبار العراقية</div>
                    <div class="source">مصادر RSS متنوعة</div>
                </div>
            </div>

            <div class="section">
                <h2>🔧 أدوات الإدارة</h2>
                <a href="/health" class="btn">فحص حالة الخادم</a>
                <a href="/api/news/admin/status" class="btn">حالة النظام</a>
            </div>

            <div class="stats">
                <h3>📊 إحصائيات سريعة</h3>
                <p>API يعمل بكامل طاقته • تحديث تلقائي كل 30 دقيقة • أخبار فعلية من مصادر موثوقة</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 API الأخبار العراقية - تم التطوير بواسطة Augment Agent</p>
        </div>
    </div>
</body>
</html>`;

  res.send(htmlContent);
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'حدث خطأ في الخادم',
    message: process.env.NODE_ENV === 'development' ? err.message : 'خطأ داخلي'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'الصفحة غير موجودة',
    message: 'المسار المطلوب غير متاح'
  });
});

// Initialize database and start server
async function startServer() {
  try {
    await initDatabase();
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    
    // Start the scheduler for news updates
    startScheduler();
    console.log('✅ تم تشغيل جدولة تحديث الأخبار');
    
    app.listen(PORT, () => {
      console.log(`🚀 خادم API الأخبار العراقية يعمل على المنفذ ${PORT}`);
      console.log(`🌐 الرابط: http://localhost:${PORT}`);
      console.log(`📊 لوحة التحكم: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    console.error('❌ خطأ في تشغيل الخادم:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 إيقاف الخادم...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 إيقاف الخادم...');
  process.exit(0);
});

startServer();
