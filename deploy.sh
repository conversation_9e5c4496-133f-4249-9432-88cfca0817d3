#!/bin/bash

# Iraqi News API Deployment Script
# This script helps deploy the API to production

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="iraqi-news-api"
DOCKER_IMAGE="iraqi-news-api:latest"
CONTAINER_NAME="iraqi-news-api"
PORT=5020

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "Docker and Docker Compose are installed"
}

# Check if required files exist
check_files() {
    local required_files=("Dockerfile" "docker-compose.yml" "package.json" "server.js")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Required file $file not found"
            exit 1
        fi
    done
    
    log_success "All required files found"
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p data
    mkdir -p logs
    mkdir -p ssl
    
    log_success "Directories created"
}

# Build Docker image
build_image() {
    log_info "Building Docker image..."
    
    docker build -t $DOCKER_IMAGE .
    
    if [[ $? -eq 0 ]]; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Stop existing containers
stop_containers() {
    log_info "Stopping existing containers..."
    
    if docker ps -q --filter "name=$CONTAINER_NAME" | grep -q .; then
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME
        log_success "Existing container stopped and removed"
    else
        log_info "No existing container found"
    fi
}

# Deploy with Docker Compose
deploy_compose() {
    log_info "Deploying with Docker Compose..."
    
    # Stop existing services
    docker-compose down
    
    # Start services
    docker-compose up -d
    
    if [[ $? -eq 0 ]]; then
        log_success "Services deployed successfully"
    else
        log_error "Failed to deploy services"
        exit 1
    fi
}

# Deploy without Docker Compose (single container)
deploy_single() {
    log_info "Deploying single container..."
    
    docker run -d \
        --name $CONTAINER_NAME \
        --restart unless-stopped \
        -p $PORT:$PORT \
        -v $(pwd)/data:/app/data \
        -v $(pwd)/logs:/app/logs \
        -e NODE_ENV=production \
        -e PORT=$PORT \
        $DOCKER_IMAGE
    
    if [[ $? -eq 0 ]]; then
        log_success "Container deployed successfully"
    else
        log_error "Failed to deploy container"
        exit 1
    fi
}

# Check deployment health
check_health() {
    log_info "Checking deployment health..."
    
    # Wait for container to start
    sleep 10
    
    # Check if container is running
    if docker ps --filter "name=$CONTAINER_NAME" --filter "status=running" | grep -q $CONTAINER_NAME; then
        log_success "Container is running"
    else
        log_error "Container is not running"
        docker logs $CONTAINER_NAME
        exit 1
    fi
    
    # Check API health endpoint
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s http://localhost:$PORT/health > /dev/null; then
            log_success "API is responding to health checks"
            break
        else
            log_info "Waiting for API to respond... (attempt $attempt/$max_attempts)"
            sleep 2
            ((attempt++))
        fi
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "API failed to respond after $max_attempts attempts"
        docker logs $CONTAINER_NAME
        exit 1
    fi
}

# Show deployment info
show_info() {
    log_info "Deployment Information:"
    echo "=========================="
    echo "Application: $APP_NAME"
    echo "Port: $PORT"
    echo "Health Check: http://localhost:$PORT/health"
    echo "API Documentation: http://localhost:$PORT/"
    echo "Container Name: $CONTAINER_NAME"
    echo "=========================="
    
    log_info "Useful commands:"
    echo "View logs: docker logs $CONTAINER_NAME"
    echo "Stop container: docker stop $CONTAINER_NAME"
    echo "Restart container: docker restart $CONTAINER_NAME"
    echo "Remove container: docker rm $CONTAINER_NAME"
}

# Backup existing data
backup_data() {
    if [[ -d "data" ]] && [[ -f "data/iraqi_news.db" ]]; then
        log_info "Backing up existing data..."
        
        local backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p $backup_dir
        cp -r data $backup_dir/
        
        log_success "Data backed up to $backup_dir"
    fi
}

# Main deployment function
deploy() {
    local deployment_type=${1:-"compose"}
    
    log_info "Starting deployment of $APP_NAME..."
    
    # Pre-deployment checks
    check_docker
    check_files
    
    # Backup existing data
    backup_data
    
    # Prepare environment
    create_directories
    
    # Build and deploy
    build_image
    stop_containers
    
    if [[ "$deployment_type" == "compose" ]]; then
        deploy_compose
    else
        deploy_single
    fi
    
    # Post-deployment checks
    check_health
    show_info
    
    log_success "Deployment completed successfully!"
}

# Script usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  deploy [compose|single]  Deploy the application (default: compose)"
    echo "  stop                     Stop the application"
    echo "  restart                  Restart the application"
    echo "  logs                     Show application logs"
    echo "  status                   Show application status"
    echo "  backup                   Backup application data"
    echo "  help                     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy               # Deploy with Docker Compose"
    echo "  $0 deploy single        # Deploy single container"
    echo "  $0 stop                 # Stop the application"
    echo "  $0 logs                 # Show logs"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        deploy ${2:-compose}
        ;;
    "stop")
        log_info "Stopping application..."
        docker-compose down
        log_success "Application stopped"
        ;;
    "restart")
        log_info "Restarting application..."
        docker-compose restart
        log_success "Application restarted"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    "backup")
        backup_data
        ;;
    "help"|"-h"|"--help")
        usage
        ;;
    *)
        log_error "Unknown command: $1"
        usage
        exit 1
        ;;
esac
