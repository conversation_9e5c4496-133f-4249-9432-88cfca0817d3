#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح محسن لمحددات CSS للمصادر الفاشلة
"""

import requests
import json
import time
from database import Database
from bs4 import BeautifulSoup

class AdvancedSelectorsFix:
    def __init__(self):
        """تهيئة أداة الإصلاح المحسنة"""
        self.db = Database()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ar,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
    
    def analyze_page_structure(self, url):
        """تحليل هيكل الصفحة لإيجاد أفضل محددات"""
        try:
            response = self.session.get(url, timeout=15)
            if response.status_code != 200:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن العناصر التي تحتوي على نصوص طويلة (أخبار محتملة)
            potential_news = []
            
            # فحص جميع العناصر التي تحتوي على نص
            for element in soup.find_all(['div', 'article', 'section', 'li', 'span', 'p']):
                text = element.get_text().strip()
                if len(text) > 50 and len(text) < 500:  # نص متوسط الطول
                    # فحص إذا كان يحتوي على رابط
                    link = element.find('a')
                    if link and link.get('href'):
                        potential_news.append({
                            'element': element,
                            'text': text[:100],
                            'tag': element.name,
                            'classes': element.get('class', []),
                            'parent_classes': element.parent.get('class', []) if element.parent else [],
                            'link': link.get('href')
                        })
            
            return potential_news[:20]  # أول 20 عنصر محتمل
            
        except Exception as e:
            print(f"خطأ في تحليل الصفحة: {str(e)}")
            return None
    
    def generate_smart_selectors(self, potential_news):
        """إنشاء محددات ذكية بناءً على تحليل الصفحة"""
        if not potential_news:
            return None
        
        # تحليل الأنماط الشائعة
        common_classes = {}
        common_parent_classes = {}
        common_tags = {}
        
        for item in potential_news:
            # تحليل الكلاسات
            for cls in item['classes']:
                common_classes[cls] = common_classes.get(cls, 0) + 1
            
            # تحليل كلاسات الوالد
            for cls in item['parent_classes']:
                common_parent_classes[cls] = common_parent_classes.get(cls, 0) + 1
            
            # تحليل التاجات
            tag = item['tag']
            common_tags[tag] = common_tags.get(tag, 0) + 1
        
        # العثور على أكثر الأنماط شيوعاً
        best_class = max(common_classes.items(), key=lambda x: x[1])[0] if common_classes else None
        best_parent_class = max(common_parent_classes.items(), key=lambda x: x[1])[0] if common_parent_classes else None
        best_tag = max(common_tags.items(), key=lambda x: x[1])[0] if common_tags else None
        
        # إنشاء محددات
        selectors = []
        
        if best_class and common_classes[best_class] >= 3:
            selectors.append(f'.{best_class}')
        
        if best_parent_class and common_parent_classes[best_parent_class] >= 3:
            selectors.append(f'.{best_parent_class} > *')
        
        if best_tag and common_tags[best_tag] >= 3:
            selectors.append(best_tag)
        
        # إضافة محددات احتياطية
        selectors.extend(['article', '.news-item', '.post', '.entry', '.item'])
        
        container_selector = ', '.join(selectors[:5])  # أول 5 محددات
        
        return {
            'container': container_selector,
            'title': 'h1, h2, h3, h4, .title, .headline, a',
            'content': 'p, .content, .summary, .excerpt, .description',
            'link': 'a'
        }
    
    def test_selectors_effectiveness(self, url, selectors):
        """اختبار فعالية المحددات"""
        try:
            response = self.session.get(url, timeout=15)
            if response.status_code != 200:
                return 0, []
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            containers = soup.select(selectors['container'])
            if not containers:
                return 0, []
            
            valid_news = []
            for container in containers[:10]:
                title_elem = container.select_one(selectors['title'])
                if title_elem:
                    title_text = title_elem.get_text().strip()
                    if len(title_text) > 10:  # عنوان معقول
                        link_elem = container.select_one(selectors['link'])
                        link = link_elem.get('href') if link_elem else None
                        
                        valid_news.append({
                            'title': title_text[:100],
                            'link': link
                        })
            
            return len(valid_news), valid_news
            
        except Exception as e:
            print(f"خطأ في اختبار المحددات: {str(e)}")
            return 0, []
    
    def fix_source_with_smart_analysis(self, source):
        """إصلاح المصدر باستخدام التحليل الذكي"""
        print(f"\n🧠 تحليل ذكي للمصدر: {source['name']}")
        
        # 1. تحليل هيكل الصفحة
        potential_news = self.analyze_page_structure(source['url'])
        if not potential_news:
            print("   ❌ فشل في تحليل الصفحة")
            return None
        
        print(f"   📊 تم العثور على {len(potential_news)} عنصر محتمل")
        
        # 2. إنشاء محددات ذكية
        smart_selectors = self.generate_smart_selectors(potential_news)
        if not smart_selectors:
            print("   ❌ فشل في إنشاء محددات ذكية")
            return None
        
        print(f"   🎯 محددات ذكية: {smart_selectors['container'][:50]}...")
        
        # 3. اختبار المحددات
        score, sample_news = self.test_selectors_effectiveness(source['url'], smart_selectors)
        
        print(f"   📈 النتيجة: {score} خبر صحيح")
        
        if score >= 3:
            print("   ✅ المحددات الذكية تعمل بشكل جيد")
            if sample_news:
                print("   📰 عينة من الأخبار:")
                for news in sample_news[:3]:
                    print(f"      • {news['title']}")
            return smart_selectors
        else:
            print("   ⚠️ المحددات الذكية تحتاج تحسين")
            return None
    
    def get_fallback_selectors(self, source_name):
        """محددات احتياطية مخصصة للمواقع العراقية"""
        
        fallback_map = {
            '964 ميديا': {
                'container': '.elementor-widget-container, .elementor-element, .post, .news',
                'title': 'h1, h2, h3, .elementor-heading-title, .entry-title',
                'content': 'p, .elementor-text-editor, .entry-content',
                'link': 'a'
            },
            'السومرية نيوز': {
                'container': '.views-row, .node, .article, .news-item',
                'title': 'h1, h2, h3, .node-title, .views-field-title',
                'content': 'p, .node-content, .views-field-body',
                'link': 'a'
            },
            'الفرات نيوز': {
                'container': '.item, .news, .post, .article',
                'title': 'h1, h2, h3, .item-title, .news-title',
                'content': 'p, .item-content, .news-content',
                'link': 'a'
            },
            'بغداد اليوم': {
                'container': '.news, .post, .article, .item',
                'title': 'h1, h2, h3, .news-title, .post-title',
                'content': 'p, .news-content, .post-content',
                'link': 'a'
            },
            'صحيفة الزمان': {
                'container': '.post, .article, .news, .entry',
                'title': 'h1, h2, h3, .post-title, .entry-title',
                'content': 'p, .post-content, .entry-content',
                'link': 'a'
            },
            'صحيفة المدى': {
                'container': '.post, .article, .news, .entry',
                'title': 'h1, h2, h3, .post-title, .entry-title',
                'content': 'p, .post-content, .entry-content',
                'link': 'a'
            },
            'كتابات في الميزان': {
                'container': '.post, .article, .entry, .news',
                'title': 'h1, h2, h3, .post-title, .entry-title',
                'content': 'p, .post-content, .entry-content',
                'link': 'a'
            },
            'نون الخبرية': {
                'container': '.post, .article, .news, .entry',
                'title': 'h1, h2, h3, .post-title, .entry-title',
                'content': 'p, .post-content, .entry-content',
                'link': 'a'
            }
        }
        
        return fallback_map.get(source_name, {
            'container': 'article, .article, .post, .news, .entry, .item',
            'title': 'h1, h2, h3, .title, .headline',
            'content': 'p, .content, .summary',
            'link': 'a'
        })
    
    def fix_all_no_news_sources(self):
        """إصلاح جميع المصادر التي لا تجمع أخبار"""
        print("🚀 بدء الإصلاح المحسن للمصادر...")
        
        # جلب المصادر التي لا تجمع أخبار
        sources = self.db.get_all_sources()
        no_news_sources = [s for s in sources if s.get('status') and 'no_news' in s['status']]
        
        if not no_news_sources:
            print("✅ جميع المصادر تعمل بشكل جيد!")
            return
        
        print(f"📊 تم العثور على {len(no_news_sources)} مصدر يحتاج إصلاح")
        
        fixed_count = 0
        failed_count = 0
        
        for source in no_news_sources:
            try:
                print(f"\n🔧 إصلاح: {source['name']}")
                
                # 1. جرب التحليل الذكي أولاً
                smart_selectors = self.fix_source_with_smart_analysis(source)
                
                if smart_selectors:
                    # استخدم المحددات الذكية
                    new_selectors = smart_selectors
                    print("   🧠 استخدام محددات ذكية")
                else:
                    # استخدم المحددات الاحتياطية
                    new_selectors = self.get_fallback_selectors(source['name'])
                    print("   🔄 استخدام محددات احتياطية")
                
                # 2. تحديث المحددات في قاعدة البيانات
                update_data = {
                    'selectors': json.dumps(new_selectors, ensure_ascii=False)
                }
                
                if self.db.update_source(source['id'], update_data):
                    # إعادة تعيين حالة المصدر
                    self.db.update_source_status(source['id'], 'unknown')
                    fixed_count += 1
                    print("   ✅ تم تحديث المحددات بنجاح")
                else:
                    failed_count += 1
                    print("   ❌ فشل في تحديث المحددات")
                
                time.sleep(2)  # توقف بين المصادر
                
            except Exception as e:
                failed_count += 1
                print(f"   ❌ خطأ في إصلاح {source['name']}: {str(e)}")
        
        print(f"\n🎉 انتهى الإصلاح المحسن:")
        print(f"✅ تم إصلاح: {fixed_count} مصدر")
        print(f"❌ فشل في إصلاح: {failed_count} مصدر")
        print(f"📊 إجمالي المصادر المعالجة: {len(no_news_sources)}")
        
        return fixed_count, failed_count

if __name__ == '__main__':
    fixer = AdvancedSelectorsFix()
    fixer.fix_all_no_news_sources()
