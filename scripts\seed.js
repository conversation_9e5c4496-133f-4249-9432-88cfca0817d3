#!/usr/bin/env node

// <PERSON>ript to seed the database with sample news data
require('dotenv').config();
const { initDatabase } = require('../config/database');
const { seedDatabase, clearDatabase, resetDatabase } = require('../utils/seed-data');

async function main() {
  const command = process.argv[2];
  
  try {
    // Initialize database
    await initDatabase();
    console.log('✅ تم الاتصال بقاعدة البيانات\n');
    
    switch (command) {
      case 'seed':
        await seedDatabase();
        break;
        
      case 'clear':
        await clearDatabase();
        break;
        
      case 'reset':
        await resetDatabase();
        break;
        
      default:
        console.log('📖 استخدام الأوامر:');
        console.log('  npm run seed        - إدراج البيانات التجريبية');
        console.log('  npm run seed:clear  - مسح جميع البيانات');
        console.log('  npm run seed:reset  - إعادة تعيين البيانات');
        console.log('');
        console.log('أو استخدم:');
        console.log('  node scripts/seed.js seed');
        console.log('  node scripts/seed.js clear');
        console.log('  node scripts/seed.js reset');
        break;
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    process.exit(1);
  }
  
  process.exit(0);
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = main;
