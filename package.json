{"name": "iraqi-news-api", "version": "1.0.0", "description": "API مخصص لجلب الأخبار من المواقع الإخبارية العراقية", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test-api": "node test-api.js", "seed": "node scripts/seed.js seed", "seed:clear": "node scripts/seed.js clear", "seed:reset": "node scripts/seed.js reset"}, "keywords": ["iraq", "news", "api", "scraping", "arabic"], "author": "iNews Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "cheerio": "^1.0.0-rc.12", "axios": "^1.6.2", "puppeteer": "^21.6.1", "node-cron": "^3.0.3", "sqlite3": "^5.1.6", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}}