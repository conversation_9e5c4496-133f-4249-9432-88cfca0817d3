const cheerio = require('cheerio');
const moment = require('moment-timezone');
const News = require('../models/News');
const { makeRequest, cleanArabicText, detectCategoryFromText, globalRateLimiter } = require('../utils/scraper-helpers');

class Media964Scraper {
  constructor() {
    this.baseUrl = 'https://964media.com';
    this.source = 'media964';
    this.timezone = 'Asia/Baghdad';
  }

  // Main scraping function
  async scrapeNews() {
    try {
      console.log('🔄 بدء جمع الأخبار من 964 ميديا...');
      
      const articles = await this.getArticlesList();
      let savedCount = 0;
      let errorCount = 0;

      for (const article of articles) {
        try {
          const fullArticle = await this.getArticleDetails(article);
          if (fullArticle) {
            const news = new News(fullArticle);
            await news.save();
            savedCount++;
            console.log(`✅ تم حفظ: ${fullArticle.title.substring(0, 50)}...`);
          }
        } catch (error) {
          errorCount++;
          console.error(`❌ خطأ في حفظ المقال: ${error.message}`);
        }
      }

      console.log(`✅ 964 ميديا: تم حفظ ${savedCount} خبر، ${errorCount} خطأ`);
      return { saved: savedCount, errors: errorCount };
    } catch (error) {
      console.error('❌ خطأ في جمع أخبار 964 ميديا:', error.message);
      throw error;
    }
  }

  // Get list of articles from main page and news sections
  async getArticlesList() {
    try {
      const articles = [];
      
      // Try multiple pages/sections
      const urls = [
        this.baseUrl,
        `${this.baseUrl}/wires/iraq`,
        `${this.baseUrl}/news`,
        `${this.baseUrl}/latest`
      ];

      for (const url of urls) {
        try {
          await globalRateLimiter.waitIfNeeded();
          const pageArticles = await this.getArticlesFromPage(url);
          articles.push(...pageArticles);
        } catch (error) {
          console.error(`خطأ في جلب المقالات من ${url}:`, error.message);
        }
      }

      // Remove duplicates
      const uniqueArticles = articles.filter((article, index, self) => 
        index === self.findIndex(a => a.url === article.url)
      );

      console.log(`📰 964 ميديا: تم العثور على ${uniqueArticles.length} مقال`);
      return uniqueArticles.slice(0, 20); // Limit to 20 articles per scrape
    } catch (error) {
      console.error('خطأ في جلب قائمة المقالات من 964 ميديا:', error.message);
      return [];
    }
  }

  // Get articles from a specific page
  async getArticlesFromPage(url) {
    try {
      const response = await makeRequest(url);

      const $ = cheerio.load(response.data);
      const articles = [];

      // Extract articles using various selectors
      $('.news-item, .article-item, .post-item, .story-item, .wire-item').each((index, element) => {
        const $element = $(element);
        const titleElement = $element.find('h1, h2, h3, h4, .title, .headline').first();
        const linkElement = $element.find('a').first();
        
        if (titleElement.length && linkElement.length) {
          const title = titleElement.text().trim();
          const relativeUrl = linkElement.attr('href');
          
          if (title && relativeUrl) {
            const fullUrl = relativeUrl.startsWith('http') ? relativeUrl : `${this.baseUrl}${relativeUrl}`;
            
            articles.push({
              title: title,
              url: fullUrl,
              summary: $element.find('.excerpt, .summary, .description').text().trim() || null
            });
          }
        }
      });

      // Try alternative selectors
      $('article, .news-card, .post-card, .content-item, .media-item').each((index, element) => {
        const $element = $(element);
        const titleElement = $element.find('h1, h2, h3, .post-title, .entry-title, .media-title').first();
        const linkElement = $element.find('a[href*="/wires/"], a[href*="/news/"], a[href*="/link/"]').first();
        
        if (titleElement.length && linkElement.length) {
          const title = titleElement.text().trim();
          const relativeUrl = linkElement.attr('href');
          
          if (title && relativeUrl && title.length > 10) {
            const fullUrl = relativeUrl.startsWith('http') ? relativeUrl : `${this.baseUrl}${relativeUrl}`;
            
            // Avoid duplicates
            if (!articles.some(article => article.url === fullUrl)) {
              articles.push({
                title: title,
                url: fullUrl,
                summary: $element.find('.excerpt, .summary, .content').text().trim().substring(0, 200) || null
              });
            }
          }
        }
      });

      // Extract from news lists and wire feeds
      $('.news-list li, .wire-list li, .article-list li, ul.posts li').each((index, element) => {
        const $element = $(element);
        const linkElement = $element.find('a').first();
        
        if (linkElement.length) {
          const title = linkElement.text().trim() || linkElement.attr('title');
          const relativeUrl = linkElement.attr('href');
          
          if (title && relativeUrl && title.length > 10) {
            const fullUrl = relativeUrl.startsWith('http') ? relativeUrl : `${this.baseUrl}${relativeUrl}`;
            
            if (!articles.some(article => article.url === fullUrl)) {
              articles.push({
                title: title,
                url: fullUrl,
                summary: null
              });
            }
          }
        }
      });

      return articles;
    } catch (error) {
      console.error(`خطأ في جلب المقالات من الصفحة ${url}:`, error.message);
      return [];
    }
  }

  // Get full article details
  async getArticleDetails(article) {
    try {
      await globalRateLimiter.waitIfNeeded();
      const response = await makeRequest(article.url);

      const $ = cheerio.load(response.data);
      
      // Extract content
      const content = this.extractContent($);
      const publishedDate = this.extractPublishedDate($);
      const author = this.extractAuthor($);
      const category = this.extractCategory($, article.url);
      const imageUrl = this.extractImageUrl($);
      const tags = this.extractTags($);

      return {
        title: cleanArabicText(article.title),
        content: cleanArabicText(content),
        summary: cleanArabicText(article.summary) || cleanArabicText(content).substring(0, 200) + '...',
        url: article.url,
        source: this.source,
        category: category || detectCategoryFromText(article.title + ' ' + content),
        author: author,
        published_date: publishedDate,
        image_url: imageUrl,
        tags: tags
      };
    } catch (error) {
      console.error(`خطأ في جلب تفاصيل المقال ${article.url}:`, error.message);
      return null;
    }
  }

  // Extract article content
  extractContent($) {
    const contentSelectors = [
      '.wire-content',
      '.article-content',
      '.post-content',
      '.entry-content',
      '.content',
      '.story-content',
      '.news-content',
      '.main-content',
      'article .content',
      '.post-body',
      '.media-content'
    ];

    for (const selector of contentSelectors) {
      const content = $(selector).text().trim();
      if (content && content.length > 100) {
        // Clean up content
        return content.replace(/\s+/g, ' ').trim();
      }
    }

    // Fallback: get all paragraphs
    const paragraphs = $('p').map((i, el) => $(el).text().trim()).get();
    const validParagraphs = paragraphs.filter(p => p.length > 20);
    return validParagraphs.join('\n\n');
  }

  // Extract published date
  extractPublishedDate($) {
    const dateSelectors = [
      '.wire-date',
      '.post-date',
      '.date',
      '.publish-date',
      '.article-date',
      'time',
      '.timestamp',
      '.entry-date',
      '[datetime]'
    ];

    for (const selector of dateSelectors) {
      const dateElement = $(selector).first();
      if (dateElement.length) {
        const dateText = dateElement.attr('datetime') || dateElement.text().trim();
        
        // Try to parse different date formats
        const formats = [
          'YYYY-MM-DD HH:mm:ss',
          'YYYY-MM-DD',
          'DD/MM/YYYY',
          'MM/DD/YYYY',
          'YYYY/MM/DD',
          'DD-MM-YYYY',
          'YYYY-MM-DDTHH:mm:ss'
        ];
        
        for (const format of formats) {
          const parsedDate = moment.tz(dateText, format, this.timezone);
          if (parsedDate.isValid()) {
            return parsedDate.format('YYYY-MM-DD HH:mm:ss');
          }
        }
      }
    }

    // Default to current time if no date found
    return moment.tz(this.timezone).format('YYYY-MM-DD HH:mm:ss');
  }

  // Extract author
  extractAuthor($) {
    const authorSelectors = [
      '.wire-source',
      '.author',
      '.by-author',
      '.post-author',
      '.article-author',
      '.writer',
      '.byline',
      '.author-name',
      '.source'
    ];

    for (const selector of authorSelectors) {
      const author = $(selector).text().trim();
      if (author) {
        return author.replace(/^(بقلم|كتب|المحرر|الكاتب|بواسطة|المصدر):?\s*/i, '');
      }
    }

    return '964 ميديا';
  }

  // Extract category
  extractCategory($, url) {
    const categorySelectors = [
      '.wire-category',
      '.category',
      '.post-category',
      '.article-category',
      '.section',
      '.breadcrumb a:last-child',
      '.cat-links a',
      '.entry-category'
    ];

    for (const selector of categorySelectors) {
      const category = $(selector).text().trim();
      if (category) {
        return this.mapCategory(category);
      }
    }

    // Try to extract from URL
    if (url.includes('/wires/iraq')) return 'general';
    if (url.includes('/politics')) return 'politics';
    if (url.includes('/economy')) return 'economy';
    if (url.includes('/security')) return 'security';
    if (url.includes('/sports')) return 'sports';

    return 'general';
  }

  // Extract image URL
  extractImageUrl($) {
    const imageSelectors = [
      '.wire-image img',
      '.post-image img',
      '.article-image img',
      '.featured-image img',
      '.entry-image img',
      '.content img:first',
      'article img:first',
      '.thumbnail img',
      '.media-image img'
    ];

    for (const selector of imageSelectors) {
      const img = $(selector).first();
      if (img.length) {
        const src = img.attr('src') || img.attr('data-src') || img.attr('data-lazy-src');
        if (src) {
          return src.startsWith('http') ? src : `${this.baseUrl}${src}`;
        }
      }
    }

    return null;
  }

  // Extract tags
  extractTags($) {
    const tags = [];
    
    $('.tag, .tags a, .post-tags a, .entry-tags a, .wire-tags a').each((i, el) => {
      const tag = $(el).text().trim();
      if (tag) tags.push(tag);
    });

    return tags.length > 0 ? tags.join(',') : null;
  }

  // Map Arabic categories to English
  mapCategory(arabicCategory) {
    const categoryMap = {
      'سياسة': 'politics',
      'سياسي': 'politics',
      'اقتصاد': 'economy',
      'اقتصادي': 'economy',
      'أمن': 'security',
      'أمني': 'security',
      'رياضة': 'sports',
      'رياضي': 'sports',
      'ثقافة': 'culture',
      'ثقافي': 'culture',
      'تكنولوجيا': 'technology',
      'تقنية': 'technology',
      'صحة': 'health',
      'طب': 'health',
      'محلي': 'general',
      'محلية': 'general',
      'عربي': 'general',
      'دولي': 'general',
      'عام': 'general',
      'عراق': 'general'
    };

    const lowerCategory = arabicCategory.toLowerCase();
    for (const [arabic, english] of Object.entries(categoryMap)) {
      if (lowerCategory.includes(arabic)) {
        return english;
      }
    }

    return 'general';
  }
}

module.exports = Media964Scraper;
