#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام أخبار العراق - Iraqi News System
تطبيق Flask لجمع وعرض أخبار العراق فقط
"""

from flask import Flask, jsonify, render_template_string, request, redirect, url_for
from flask_cors import CORS
import sqlite3
import threading
import schedule
import time
from datetime import datetime, timedelta
import pytz
import logging
from news_scraper import NewsScraper
from database import Database
from source_manager import SourceManager

# إعداد التطبيق
app = Flask(__name__)
CORS(app)

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('iraqi_news.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعداد قاعدة البيانات
db = Database()
scraper = NewsScraper(db)
source_manager = SourceManager(db)

# المنطقة الزمنية العراقية
IRAQ_TZ = pytz.timezone('Asia/Baghdad')

# متغير لتتبع حالة جمع الأخبار
news_collection_status = {
    'is_running': False,
    'last_run': None,
    'total_collected': 0,
    'current_source': '',
    'progress': 0
}

def run_scheduler():
    """تشغيل جدولة المهام في خيط منفصل"""
    while True:
        schedule.run_pending()
        time.sleep(60)

def collect_news():
    """جمع الأخبار من جميع المصادر"""
    global news_collection_status

    try:
        # تحديث حالة البدء
        news_collection_status['is_running'] = True
        news_collection_status['current_source'] = 'بدء العملية...'
        news_collection_status['progress'] = 0

        logger.info("🚀 بدء عملية جمع الأخبار...")
        result = scraper.scrape_all_sources()

        # تحديث حالة الانتهاء
        news_collection_status['is_running'] = False
        news_collection_status['last_run'] = datetime.now(IRAQ_TZ).isoformat()
        news_collection_status['total_collected'] = result['total_saved']
        news_collection_status['current_source'] = 'مكتمل'
        news_collection_status['progress'] = 100

        logger.info(f"✅ تم جمع {result['total_saved']} خبر جديد")
        return result
    except Exception as e:
        # تحديث حالة الخطأ
        news_collection_status['is_running'] = False
        news_collection_status['current_source'] = f'خطأ: {str(e)}'

        logger.error(f"❌ خطأ في جمع الأخبار: {str(e)}")
        return {"total_saved": 0, "errors": [str(e)]}

# جدولة جمع الأخبار كل 30 دقيقة
schedule.every(30).minutes.do(collect_news)

# بدء جدولة المهام
scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
scheduler_thread.start()

@app.route('/')
def home():
    """الصفحة الرئيسية"""
    return render_template_string("""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇮🇶 نظام أخبار العراق</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.3em;
            opacity: 0.9;
        }
        .nav {
            background: #34495e;
            padding: 20px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
            display: inline-block;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            border-right: 5px solid #e74c3c;
            transition: transform 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-5px);
        }
        .feature h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        .feature p {
            color: #666;
            line-height: 1.6;
        }
        .stats {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
        }
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
        }
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.9em;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .status-indicator.collecting {
            background: #f39c12;
            animation: pulse 2s infinite;
        }
        .status-indicator.error {
            background: #e74c3c;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- مؤشر حالة جمع الأخبار -->
    <div id="statusIndicator" class="status-indicator">
        🔄 جاري التحقق من الحالة...
    </div>

    <div class="container">
        <div class="header">
            <h1>🇮🇶 نظام أخبار العراق</h1>
            <p>مصدرك الموثوق للأخبار العراقية المحدثة</p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 الأخبار</a>
            <a href="/admin">⚙️ الإدارة</a>
            <a href="/sources">🔧 المصادر</a>
        </div>

        <div class="content">
            <div class="features">
                <div class="feature">
                    <h3>📰 أخبار العراق فقط</h3>
                    <p>نركز حصرياً على الأخبار العراقية المهمة من مصادر موثوقة</p>
                </div>
                <div class="feature">
                    <h3>🕐 تحديث مستمر</h3>
                    <p>جمع تلقائي للأخبار كل 30 دقيقة من مصادر متعددة</p>
                </div>
                <div class="feature">
                    <h3>🎯 تصفية ذكية</h3>
                    <p>استبعاد أخبار الفن والثقافة والطقس والرياضة</p>
                </div>
                <div class="feature">
                    <h3>📅 أخبار اليوم</h3>
                    <p>عرض أخبار اليوم الحالي فقط مع التاريخ العراقي</p>
                </div>
            </div>

            <div class="stats">
                <h3>📊 إحصائيات سريعة</h3>
                <p>نظام متطور لجمع وعرض الأخبار العراقية بتقنية Python و Flask</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 نظام أخبار العراق - تم التطوير بواسطة Python & Flask</p>
        </div>
    </div>

    <script>
        // تحديث حالة جمع الأخبار
        async function updateNewsStatus() {
            try {
                const response = await fetch('/api/news/status');
                const result = await response.json();

                if (result.success) {
                    const status = result.data;
                    const indicator = document.getElementById('statusIndicator');

                    if (status.is_running) {
                        indicator.textContent = `🔄 جاري جمع الأخبار... (${status.current_source})`;
                        indicator.className = 'status-indicator collecting';
                    } else if (status.last_run) {
                        indicator.textContent = `✅ آخر جمع: ${status.total_collected} خبر`;
                        indicator.className = 'status-indicator';
                    } else {
                        indicator.textContent = '🔄 في انتظار بدء جمع الأخبار...';
                        indicator.className = 'status-indicator';
                    }
                }
            } catch (error) {
                const indicator = document.getElementById('statusIndicator');
                indicator.textContent = '❌ خطأ في الاتصال';
                indicator.className = 'status-indicator error';
            }
        }

        // تحديث الحالة كل 3 ثوان
        setInterval(updateNewsStatus, 3000);

        // تحديث فوري عند تحميل الصفحة
        updateNewsStatus();
    </script>
</body>
</html>
    """)

@app.route('/api/news')
def api_news():
    """API لجلب الأخبار"""
    try:
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        page = request.args.get('page', 1, type=int)
        category = request.args.get('category', '')
        search = request.args.get('search', '')

        # إذا تم تمرير page، احسب offset
        if page > 1:
            offset = (page - 1) * limit

        if search:
            news = db.search_news(search, limit * 10)  # جلب أكثر للبحث
            total_count = len(news)
            news = news[offset:offset + limit]
        elif category:
            news = db.get_news_by_category(category, limit * 10)
            total_count = len(news)
            news = news[offset:offset + limit]
        else:
            all_news = db.get_all_news(limit * 10, 0)
            total_count = len(all_news)
            news = all_news[offset:offset + limit]

        total_pages = (total_count + limit - 1) // limit

        return jsonify({
            'success': True,
            'data': news,
            'count': len(news),
            'total_count': total_count,
            'page': page,
            'total_pages': total_pages,
            'per_page': limit
        })
    except Exception as e:
        logger.error(f"خطأ في API الأخبار: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/today')
def api_today_news():
    """API لجلب أخبار اليوم"""
    try:
        limit = request.args.get('limit', 50, type=int)
        news = db.get_today_news(limit)

        return jsonify({
            'success': True,
            'data': news,
            'count': len(news)
        })
    except Exception as e:
        logger.error(f"خطأ في API أخبار اليوم: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/stats')
def api_stats():
    """API لجلب إحصائيات الأخبار"""
    try:
        stats = db.get_stats()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"خطأ في API الإحصائيات: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/collect', methods=['POST'])
def api_collect_news():
    """API لجمع الأخبار يدوياً"""
    try:
        result = collect_news()
        return jsonify({
            'success': True,
            'message': 'تم جمع الأخبار بنجاح',
            'data': result
        })
    except Exception as e:
        logger.error(f"خطأ في جمع الأخبار: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/clear', methods=['POST'])
def api_clear_news():
    """API لحذف جميع الأخبار"""
    try:
        count = db.clear_all_news()
        return jsonify({
            'success': True,
            'message': f'تم حذف {count} خبر',
            'deleted_count': count
        })
    except Exception as e:
        logger.error(f"خطأ في حذف الأخبار: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/news/status')
def api_news_status():
    """API لحالة جمع الأخبار"""
    try:
        return jsonify({
            'success': True,
            'data': news_collection_status
        })
    except Exception as e:
        logger.error(f"خطأ في API حالة الأخبار: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== API إدارة المصادر ====================

@app.route('/api/sources')
def api_get_sources():
    """API لجلب جميع المصادر"""
    try:
        sources = db.get_all_sources()
        return jsonify({
            'success': True,
            'data': sources,
            'count': len(sources)
        })
    except Exception as e:
        logger.error(f"خطأ في API المصادر: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sources', methods=['POST'])
def api_add_source():
    """API لإضافة مصدر جديد"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('name') or not data.get('url'):
            return jsonify({
                'success': False,
                'error': 'الاسم والرابط مطلوبان'
            }), 400

        success = db.add_source(data)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم إضافة المصدر بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في إضافة المصدر'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في إضافة المصدر: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sources/<int:source_id>', methods=['PUT'])
def api_update_source(source_id):
    """API لتحديث مصدر"""
    try:
        data = request.get_json()

        success = db.update_source(source_id, data)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم تحديث المصدر بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في تحديث المصدر'
            }), 404

    except Exception as e:
        logger.error(f"خطأ في تحديث المصدر: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sources/<int:source_id>', methods=['DELETE'])
def api_delete_source(source_id):
    """API لحذف مصدر"""
    try:
        success = db.delete_source(source_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم حذف المصدر بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في حذف المصدر'
            }), 404

    except Exception as e:
        logger.error(f"خطأ في حذف المصدر: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sources/<int:source_id>/toggle', methods=['POST'])
def api_toggle_source(source_id):
    """API لتبديل حالة تفعيل المصدر"""
    try:
        success = db.toggle_source_status(source_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم تبديل حالة المصدر بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في تبديل حالة المصدر'
            }), 404

    except Exception as e:
        logger.error(f"خطأ في تبديل حالة المصدر: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sources/check', methods=['POST'])
def api_check_sources():
    """API لفحص جميع المصادر"""
    try:
        # تشغيل فحص المصادر في خيط منفصل
        def check_sources_async():
            return source_manager.check_all_sources()

        import threading
        check_thread = threading.Thread(target=check_sources_async, daemon=True)
        check_thread.start()

        return jsonify({
            'success': True,
            'message': 'تم بدء فحص المصادر في الخلفية'
        })

    except Exception as e:
        logger.error(f"خطأ في فحص المصادر: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sources/<int:source_id>/check', methods=['POST'])
def api_check_single_source(source_id):
    """API لفحص مصدر واحد"""
    try:
        source = db.get_source_by_id(source_id)
        if not source:
            return jsonify({
                'success': False,
                'error': 'المصدر غير موجود'
            }), 404

        # فحص الاتصال
        is_connected, conn_msg = source_manager.check_source_connectivity(source)

        # فحص جمع الأخبار
        can_scrape, scrape_msg, news_count = False, "لم يتم الفحص", 0
        if is_connected:
            can_scrape, scrape_msg, news_count = source_manager.test_source_scraping(source)

        # تحديث حالة المصدر
        if is_connected and can_scrape:
            db.update_source_status(source_id, 'success')
            status = 'نجح'
        else:
            error_msg = conn_msg if not is_connected else scrape_msg
            db.update_source_status(source_id, 'error', error_msg)
            status = 'فشل'

        return jsonify({
            'success': True,
            'data': {
                'status': status,
                'connection': is_connected,
                'connection_msg': conn_msg,
                'scraping': can_scrape,
                'scraping_msg': scrape_msg,
                'news_count': news_count
            }
        })

    except Exception as e:
        logger.error(f"خطأ في فحص المصدر: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sources/stats')
def api_sources_stats():
    """API لإحصائيات المصادر"""
    try:
        stats = db.get_sources_stats()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"خطأ في إحصائيات المصادر: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/news')
def news_page():
    """صفحة عرض الأخبار"""
    try:
        # الحصول على رقم الصفحة من المعاملات
        page = request.args.get('page', 1, type=int)
        per_page = 20  # عدد الأخبار في كل صفحة
        offset = (page - 1) * per_page

        # جلب أخبار اليوم أولاً، ثم جميع الأخبار
        today_news = db.get_today_news(1000)  # جلب جميع أخبار اليوم
        if today_news:
            total_news = today_news
        else:
            total_news = db.get_all_news(1000)  # جلب جميع الأخبار

        # حساب إجمالي الصفحات
        total_count = len(total_news)
        total_pages = (total_count + per_page - 1) // per_page

        # جلب أخبار الصفحة الحالية
        start_idx = offset
        end_idx = start_idx + per_page
        news = total_news[start_idx:end_idx]

        return render_template_string("""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📰 أخبار العراق اليوم</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .clear-btn {
            background: #e74c3c;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .clear-btn:hover {
            background: #c0392b;
            transform: scale(1.05);
        }
        .content {
            padding: 30px;
        }
        .news-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-right: 5px solid #e74c3c;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .news-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .news-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .news-source {
            background: #27ae60;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: bold;
        }
        .news-category {
            background: #f39c12;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
        }
        .news-date {
            color: #7f8c8d;
        }
        .no-news {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 10px;
        }
        .pagination a, .pagination span {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        .pagination a:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        .pagination .current {
            background: #e74c3c;
            color: white;
            border-color: #e74c3c;
        }
        .pagination .disabled {
            color: #ccc;
            cursor: not-allowed;
        }
        .news-stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📰 أخبار العراق اليوم</h1>
            <p>آخر الأخبار العراقية المحدثة</p>
        </div>

        <div class="nav">
            <div>
                <a href="/">🏠 الرئيسية</a>
                <a href="/news">📰 الأخبار</a>
                <a href="/admin">⚙️ الإدارة</a>
            </div>
            <button class="clear-btn" onclick="clearNews()">🗑️ تنظيف الأخبار</button>
        </div>

        <div class="content">
            <!-- إحصائيات الأخبار -->
            <div class="news-stats">
                <strong>📊 إجمالي الأخبار: {{ total_count }}</strong> |
                <strong>📄 الصفحة: {{ page }} من {{ total_pages }}</strong> |
                <strong>📰 عرض: {{ news|length }} خبر</strong>
            </div>

            {% if news %}
                {% for item in news %}
                <div class="news-item" onclick="openNews('{{ item.url }}')">
                    <div class="news-title">{{ item.title }}</div>
                    <div class="news-meta">
                        <span class="news-source">{{ item.source }}</span>
                        <span class="news-category">{{ item.category }}</span>
                        <span class="news-date">🕐 {{ item.published_date }}</span>
                    </div>
                </div>
                {% endfor %}

                <!-- نظام الصفحات -->
                {% if total_pages > 1 %}
                <div class="pagination">
                    <!-- الصفحة السابقة -->
                    {% if page > 1 %}
                        <a href="/news?page={{ page - 1 }}">« السابق</a>
                    {% else %}
                        <span class="disabled">« السابق</span>
                    {% endif %}

                    <!-- أرقام الصفحات -->
                    {% for p in range(1, total_pages + 1) %}
                        {% if p == page %}
                            <span class="current">{{ p }}</span>
                        {% elif p <= 3 or p > total_pages - 3 or (p >= page - 2 and p <= page + 2) %}
                            <a href="/news?page={{ p }}">{{ p }}</a>
                        {% elif p == 4 and page > 6 %}
                            <span>...</span>
                        {% elif p == total_pages - 3 and page < total_pages - 5 %}
                            <span>...</span>
                        {% endif %}
                    {% endfor %}

                    <!-- الصفحة التالية -->
                    {% if page < total_pages %}
                        <a href="/news?page={{ page + 1 }}">التالي »</a>
                    {% else %}
                        <span class="disabled">التالي »</span>
                    {% endif %}
                </div>
                {% endif %}
            {% else %}
                <div class="no-news">
                    <h2>📭 لا توجد أخبار متاحة</h2>
                    <p>لم يتم العثور على أخبار عراقية اليوم</p>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        function openNews(url) {
            if (url && url !== 'None') {
                window.open(url, '_blank');
            }
        }

        async function clearNews() {
            if (!confirm('هل أنت متأكد من حذف جميع الأخبار؟')) return;

            try {
                const response = await fetch('/api/news/clear', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(`تم حذف ${result.deleted_count} خبر بنجاح!`);
                    window.location.reload();
                } else {
                    alert('حدث خطأ: ' + result.error);
                }
            } catch (error) {
                alert('حدث خطأ في الاتصال: ' + error.message);
            }
        }
    </script>
</body>
</html>
        """, news=news, page=page, total_pages=total_pages, total_count=total_count)
    except Exception as e:
        logger.error(f"خطأ في صفحة الأخبار: {str(e)}")
        return f"خطأ: {str(e)}", 500

@app.route('/admin')
def admin_page():
    """صفحة الإدارة"""
    try:
        stats = db.get_stats()

        return render_template_string("""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ إدارة نظام الأخبار</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-right: 5px solid #3498db;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .action-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }
        .action-btn {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .action-btn:hover {
            transform: scale(1.05);
        }
        .action-btn.danger {
            background: #e74c3c;
        }
        .action-btn.success {
            background: #27ae60;
        }
        .log {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: monospace;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ إدارة نظام الأخبار</h1>
            <p>لوحة التحكم والإدارة</p>
        </div>

        <div class="nav">
            <a href="/">🏠 الرئيسية</a>
            <a href="/news">📰 الأخبار</a>
            <a href="/admin">⚙️ الإدارة</a>
            <a href="/sources">🔧 المصادر</a>
        </div>

        <div class="content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ stats.total_news }}</div>
                    <div class="stat-label">إجمالي الأخبار</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.today_news }}</div>
                    <div class="stat-label">أخبار اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.sources|length }}</div>
                    <div class="stat-label">المصادر النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.categories|length }}</div>
                    <div class="stat-label">الفئات المتاحة</div>
                </div>
            </div>

            <div class="actions">
                <div class="action-card">
                    <h3>🔄 جمع الأخبار</h3>
                    <p>تشغيل عملية جمع الأخبار يدوياً</p>
                    <button class="action-btn success" onclick="collectNews()">
                        🔄 جمع الأخبار الآن
                    </button>
                </div>

                <div class="action-card">
                    <h3>🗑️ تنظيف الأخبار</h3>
                    <p>حذف جميع الأخبار من قاعدة البيانات</p>
                    <button class="action-btn danger" onclick="clearNews()">
                        🗑️ حذف جميع الأخبار
                    </button>
                </div>
            </div>

            <div id="log" class="log">
                <div>📋 سجل العمليات:</div>
            </div>
        </div>
    </div>

    <script>
        function addLog(message) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            log.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        async function collectNews() {
            addLog('🔄 بدء عملية جمع الأخبار...');

            try {
                const response = await fetch('/api/news/collect', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLog(`✅ تم جمع ${result.data.total_saved} خبر جديد`);
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    addLog(`❌ خطأ: ${result.error}`);
                }
            } catch (error) {
                addLog(`💥 خطأ في الاتصال: ${error.message}`);
            }
        }

        async function clearNews() {
            if (!confirm('هل أنت متأكد من حذف جميع الأخبار؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                return;
            }

            addLog('🗑️ بدء عملية حذف الأخبار...');

            try {
                const response = await fetch('/api/news/clear', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    addLog(`✅ تم حذف ${result.deleted_count} خبر`);
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    addLog(`❌ خطأ: ${result.error}`);
                }
            } catch (error) {
                addLog(`💥 خطأ في الاتصال: ${error.message}`);
            }
        }

        // تحديث الصفحة كل دقيقة
        setInterval(() => {
            window.location.reload();
        }, 60000);
    </script>
</body>
</html>
        """, stats=stats)
    except Exception as e:
        logger.error(f"خطأ في صفحة الإدارة: {str(e)}")
        return f"خطأ: {str(e)}", 500

@app.route('/sources')
def sources_page():
    """صفحة إدارة المصادر"""
    try:
        return render_template_string("""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 إدارة المصادر - نظام أخبار العراق</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #8e44ad, #9b59b6);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .nav {
            background: #2c3e50;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .nav a:hover {
            background: #3498db;
            transform: translateY(-2px);
        }
        .content {
            padding: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-right: 5px solid #3498db;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-success {
            background: #27ae60;
            color: white;
        }
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        .btn:hover {
            transform: scale(1.05);
        }
        .sources-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th, .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        .table th {
            background: #34495e;
            color: white;
            font-weight: bold;
        }
        .table tr:hover {
            background: #f8f9fa;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        .status-success {
            background: #d1ecf1;
            color: #0c5460;
        }
        .status-error {
            background: #f5c6cb;
            color: #721c24;
        }
        .status-unknown {
            background: #e2e3e5;
            color: #383d41;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            border-color: #3498db;
            outline: none;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إدارة المصادر</h1>
            <p>إدارة وفحص مصادر الأخبار العراقية</p>
        </div>

        <div class="nav">
            <div>
                <a href="/">🏠 الرئيسية</a>
                <a href="/news">📰 الأخبار</a>
                <a href="/admin">⚙️ الإدارة</a>
                <a href="/sources">🔧 المصادر</a>
            </div>
        </div>

        <div class="content">
            <!-- إحصائيات المصادر -->
            <div id="statsGrid" class="stats-grid">
                <div class="loading">🔄 جاري تحميل الإحصائيات...</div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="actions">
                <button class="btn btn-primary" onclick="showAddModal()">➕ إضافة مصدر جديد</button>
                <button class="btn btn-success" onclick="checkAllSources()">🔍 فحص جميع المصادر</button>
                <button class="btn btn-warning" onclick="refreshSources()">🔄 تحديث القائمة</button>
            </div>

            <!-- جدول المصادر -->
            <div class="sources-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الإجراءات</th>
                            <th>الحالة</th>
                            <th>آخر فحص</th>
                            <th>النوع</th>
                            <th>الرابط</th>
                            <th>الاسم</th>
                            <th>المعرف</th>
                        </tr>
                    </thead>
                    <tbody id="sourcesTableBody">
                        <tr>
                            <td colspan="7" class="loading">🔄 جاري تحميل المصادر...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل مصدر -->
    <div id="sourceModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">إضافة مصدر جديد</h2>
            <form id="sourceForm">
                <input type="hidden" id="sourceId" value="">

                <div class="form-group">
                    <label for="sourceName">اسم المصدر *</label>
                    <input type="text" id="sourceName" required>
                </div>

                <div class="form-group">
                    <label for="sourceUrl">رابط المصدر *</label>
                    <input type="url" id="sourceUrl" required>
                </div>

                <div class="form-group">
                    <label for="sourceType">نوع المصدر</label>
                    <select id="sourceType">
                        <option value="website">موقع ويب</option>
                        <option value="rss">تغذية RSS</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="sourceActive">حالة التفعيل</label>
                    <select id="sourceActive">
                        <option value="true">نشط</option>
                        <option value="false">معطل</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="sourceSelectors">محددات CSS (للمواقع)</label>
                    <textarea id="sourceSelectors" rows="4" placeholder='{"container": "article", "title": "h2", "content": "p", "link": "a"}'></textarea>
                </div>

                <div class="form-group">
                    <label for="sourceDescription">الوصف</label>
                    <textarea id="sourceDescription" rows="3"></textarea>
                </div>

                <div class="actions">
                    <button type="submit" class="btn btn-primary">💾 حفظ</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal()">❌ إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let sources = [];
        let stats = {};

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadSources();
        });

        // تحميل الإحصائيات
        async function loadStats() {
            try {
                const response = await fetch('/api/sources/stats');
                const result = await response.json();

                if (result.success) {
                    stats = result.data;
                    displayStats();
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // عرض الإحصائيات
        function displayStats() {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${stats.total_sources || 0}</div>
                    <div class="stat-label">إجمالي المصادر</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.active_sources || 0}</div>
                    <div class="stat-label">المصادر النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.inactive_sources || 0}</div>
                    <div class="stat-label">المصادر المعطلة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.types ? stats.types.length : 0}</div>
                    <div class="stat-label">أنواع المصادر</div>
                </div>
            `;
        }

        // تحميل المصادر
        async function loadSources() {
            try {
                const response = await fetch('/api/sources');
                const result = await response.json();

                if (result.success) {
                    sources = result.data;
                    displaySources();
                }
            } catch (error) {
                console.error('خطأ في تحميل المصادر:', error);
                document.getElementById('sourcesTableBody').innerHTML =
                    '<tr><td colspan="7" style="color: red;">❌ خطأ في تحميل المصادر</td></tr>';
            }
        }

        // عرض المصادر
        function displaySources() {
            const tbody = document.getElementById('sourcesTableBody');

            if (sources.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="loading">📭 لا توجد مصادر</td></tr>';
                return;
            }

            tbody.innerHTML = sources.map(source => `
                <tr>
                    <td>
                        <button class="btn btn-primary" onclick="editSource(${source.id})" style="margin: 2px; padding: 6px 12px; font-size: 12px;">✏️</button>
                        <button class="btn btn-warning" onclick="checkSource(${source.id})" style="margin: 2px; padding: 6px 12px; font-size: 12px;">🔍</button>
                        <button class="btn ${source.is_active ? 'btn-danger' : 'btn-success'}" onclick="toggleSource(${source.id})" style="margin: 2px; padding: 6px 12px; font-size: 12px;">
                            ${source.is_active ? '⏸️' : '▶️'}
                        </button>
                        <button class="btn btn-danger" onclick="deleteSource(${source.id})" style="margin: 2px; padding: 6px 12px; font-size: 12px;">🗑️</button>
                    </td>
                    <td>
                        <span class="status-badge ${source.is_active ? 'status-active' : 'status-inactive'}">
                            ${source.is_active ? 'نشط' : 'معطل'}
                        </span>
                        <br>
                        <span class="status-badge ${getStatusClass(source.status)}">
                            ${getStatusText(source.status)}
                        </span>
                    </td>
                    <td>${source.last_check ? new Date(source.last_check).toLocaleString('ar-EG') : 'لم يتم الفحص'}</td>
                    <td>${source.type === 'rss' ? 'RSS' : 'موقع ويب'}</td>
                    <td><a href="${source.url}" target="_blank" style="color: #3498db;">${source.url.substring(0, 50)}...</a></td>
                    <td><strong>${source.name}</strong></td>
                    <td>${source.id}</td>
                </tr>
            `).join('');
        }

        function getStatusClass(status) {
            if (!status || status === 'unknown') return 'status-unknown';
            if (status === 'success') return 'status-success';
            if (status.includes('error')) return 'status-error';
            return 'status-unknown';
        }

        function getStatusText(status) {
            if (!status || status === 'unknown') return 'غير معروف';
            if (status === 'success') return 'نجح';
            if (status.includes('error')) return 'خطأ';
            return status;
        }

        // إظهار نافذة الإضافة
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة مصدر جديد';
            document.getElementById('sourceForm').reset();
            document.getElementById('sourceId').value = '';
            document.getElementById('sourceModal').style.display = 'block';
        }

        // تعديل مصدر
        function editSource(id) {
            const source = sources.find(s => s.id === id);
            if (!source) return;

            document.getElementById('modalTitle').textContent = 'تعديل المصدر';
            document.getElementById('sourceId').value = source.id;
            document.getElementById('sourceName').value = source.name;
            document.getElementById('sourceUrl').value = source.url;
            document.getElementById('sourceType').value = source.type;
            document.getElementById('sourceActive').value = source.is_active.toString();
            document.getElementById('sourceSelectors').value = source.selectors || '';
            document.getElementById('sourceDescription').value = source.description || '';
            document.getElementById('sourceModal').style.display = 'block';
        }

        // إغلاق النافذة
        function closeModal() {
            document.getElementById('sourceModal').style.display = 'none';
        }

        // حفظ المصدر
        document.getElementById('sourceForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const sourceId = document.getElementById('sourceId').value;
            const data = {
                name: document.getElementById('sourceName').value,
                url: document.getElementById('sourceUrl').value,
                type: document.getElementById('sourceType').value,
                is_active: document.getElementById('sourceActive').value === 'true',
                selectors: document.getElementById('sourceSelectors').value,
                description: document.getElementById('sourceDescription').value
            };

            try {
                let response;
                if (sourceId) {
                    // تحديث
                    response = await fetch(`/api/sources/${sourceId}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                } else {
                    // إضافة
                    response = await fetch('/api/sources', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                }

                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    closeModal();
                    refreshSources();
                } else {
                    alert('خطأ: ' + result.error);
                }
            } catch (error) {
                alert('خطأ في الاتصال: ' + error.message);
            }
        });

        // حذف مصدر
        async function deleteSource(id) {
            if (!confirm('هل أنت متأكد من حذف هذا المصدر؟')) return;

            try {
                const response = await fetch(`/api/sources/${id}`, { method: 'DELETE' });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    refreshSources();
                } else {
                    alert('خطأ: ' + result.error);
                }
            } catch (error) {
                alert('خطأ في الاتصال: ' + error.message);
            }
        }

        // تبديل حالة المصدر
        async function toggleSource(id) {
            try {
                const response = await fetch(`/api/sources/${id}/toggle`, { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    refreshSources();
                } else {
                    alert('خطأ: ' + result.error);
                }
            } catch (error) {
                alert('خطأ في الاتصال: ' + error.message);
            }
        }

        // فحص مصدر واحد
        async function checkSource(id) {
            try {
                const response = await fetch(`/api/sources/${id}/check`, { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    alert(`نتيجة الفحص:
الحالة: ${data.status}
الاتصال: ${data.connection ? 'نجح' : 'فشل'} - ${data.connection_msg}
جمع الأخبار: ${data.scraping ? 'نجح' : 'فشل'} - ${data.scraping_msg}
عدد الأخبار: ${data.news_count}`);
                    refreshSources();
                } else {
                    alert('خطأ: ' + result.error);
                }
            } catch (error) {
                alert('خطأ في الاتصال: ' + error.message);
            }
        }

        // فحص جميع المصادر
        async function checkAllSources() {
            if (!confirm('هل تريد فحص جميع المصادر؟ قد يستغرق هذا بعض الوقت.')) return;

            try {
                const response = await fetch('/api/sources/check', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    // تحديث كل 5 ثوان
                    setTimeout(refreshSources, 5000);
                } else {
                    alert('خطأ: ' + result.error);
                }
            } catch (error) {
                alert('خطأ في الاتصال: ' + error.message);
            }
        }

        // تحديث البيانات
        function refreshSources() {
            loadStats();
            loadSources();
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('sourceModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
        """)
    except Exception as e:
        logger.error(f"خطأ في صفحة المصادر: {str(e)}")
        return f"خطأ: {str(e)}", 500

if __name__ == '__main__':
    # تشغيل جمع الأخبار في خيط منفصل عند بدء التطبيق
    initial_collection_thread = threading.Thread(target=collect_news, daemon=True)
    initial_collection_thread.start()

    # تشغيل التطبيق فوراً دون انتظار جمع الأخبار
    app.run(host='0.0.0.0', port=5020, debug=True)
