#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص وإصلاح المصادر الفاشلة
"""

import requests
import json
import time
from database import Database
from bs4 import BeautifulSoup
import feedparser
from urllib.parse import urlparse, urljoin

class FailedSourcesDiagnostic:
    def __init__(self):
        """تهيئة أداة التشخيص"""
        self.db = Database()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
    
    def get_failed_sources(self):
        """جلب المصادر الفاشلة"""
        sources = self.db.get_all_sources()
        failed_sources = []
        
        for source in sources:
            # المصادر التي فشلت أو لا تجمع أخبار
            if (source.get('status') and 
                ('error' in source['status'] or 
                 'HTTP' in source['status'] or 
                 'no_news' in source['status'] or
                 source['status'] == 'غير معروف')):
                failed_sources.append(source)
        
        return failed_sources
    
    def diagnose_source_detailed(self, source):
        """تشخيص مفصل للمصدر"""
        print(f"\n🔍 تشخيص مفصل للمصدر: {source['name']}")
        print(f"🔗 الرابط: {source['url']}")
        print(f"📊 الحالة الحالية: {source.get('status', 'غير معروف')}")
        
        diagnosis = {
            'source_id': source['id'],
            'source_name': source['name'],
            'url': source['url'],
            'current_status': source.get('status', 'غير معروف'),
            'issues': [],
            'solutions': [],
            'alternative_urls': [],
            'new_selectors': None,
            'recommended_action': None
        }
        
        try:
            # 1. فحص الاتصال الأساسي
            print("📡 فحص الاتصال الأساسي...")
            response = self.session.get(source['url'], timeout=15, allow_redirects=True)
            
            print(f"   📊 كود الاستجابة: {response.status_code}")
            print(f"   🔗 الرابط النهائي: {response.url}")
            print(f"   📄 نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
            
            if response.status_code != 200:
                diagnosis['issues'].append(f"HTTP {response.status_code}")
                
                # اقتراح حلول حسب كود الخطأ
                if response.status_code == 403:
                    diagnosis['solutions'].append("تغيير User-Agent أو استخدام proxy")
                elif response.status_code == 404:
                    diagnosis['solutions'].append("البحث عن رابط بديل للموقع")
                elif response.status_code in [301, 302, 308]:
                    new_url = response.headers.get('Location')
                    if new_url:
                        diagnosis['alternative_urls'].append(new_url)
                        diagnosis['solutions'].append(f"تحديث الرابط إلى: {new_url}")
                
                return diagnosis
            
            # 2. فحص نوع المحتوى
            content_type = response.headers.get('content-type', '').lower()
            
            if source['type'] == 'rss':
                return self._diagnose_rss_detailed(source, response, diagnosis)
            else:
                return self._diagnose_website_detailed(source, response, diagnosis)
                
        except requests.exceptions.Timeout:
            diagnosis['issues'].append("انتهت مهلة الاتصال")
            diagnosis['solutions'].append("زيادة مهلة الاتصال أو فحص استقرار الخادم")
        except requests.exceptions.ConnectionError:
            diagnosis['issues'].append("خطأ في الاتصال")
            diagnosis['solutions'].append("فحص صحة الرابط أو حالة الخادم")
            # اقتراح روابط بديلة
            diagnosis['alternative_urls'] = self._suggest_alternative_urls(source['url'])
        except requests.exceptions.SSLError:
            diagnosis['issues'].append("خطأ في شهادة SSL")
            diagnosis['solutions'].append("تحويل الرابط من HTTPS إلى HTTP")
            # اقتراح رابط HTTP
            if source['url'].startswith('https://'):
                diagnosis['alternative_urls'].append(source['url'].replace('https://', 'http://'))
        except Exception as e:
            diagnosis['issues'].append(f"خطأ غير متوقع: {str(e)}")
        
        return diagnosis
    
    def _diagnose_rss_detailed(self, source, response, diagnosis):
        """تشخيص مفصل لمصادر RSS"""
        print("📡 تشخيص RSS...")
        
        try:
            content_type = response.headers.get('content-type', '').lower()
            
            if 'xml' not in content_type and 'rss' not in content_type:
                diagnosis['issues'].append(f"نوع المحتوى غير صحيح: {content_type}")
                diagnosis['solutions'].append("البحث عن رابط RSS صحيح")
                return diagnosis
            
            feed = feedparser.parse(response.content)
            
            if feed.bozo:
                diagnosis['issues'].append(f"RSS غير صحيح: {feed.bozo_exception}")
                diagnosis['solutions'].append("إصلاح تنسيق RSS أو البحث عن RSS بديل")
                return diagnosis
            
            if len(feed.entries) == 0:
                diagnosis['issues'].append("RSS فارغ - لا توجد عناصر")
                diagnosis['solutions'].append("التحقق من إعدادات RSS أو البحث عن RSS نشط")
                return diagnosis
            
            # فحص جودة العناصر
            valid_entries = 0
            sample_entries = []
            
            for entry in feed.entries[:5]:
                if entry.get('title') and entry.get('link'):
                    valid_entries += 1
                    sample_entries.append({
                        'title': entry.title[:100],
                        'link': entry.link,
                        'published': entry.get('published', 'غير محدد')
                    })
            
            print(f"   📊 إجمالي العناصر: {len(feed.entries)}")
            print(f"   ✅ العناصر الصحيحة: {valid_entries}")
            
            if valid_entries == 0:
                diagnosis['issues'].append("عناصر RSS غير صحيحة")
                diagnosis['solutions'].append("فحص هيكل RSS أو البحث عن RSS بديل")
            else:
                print(f"   📰 عينة من العناصر:")
                for entry in sample_entries:
                    print(f"      • {entry['title']}")
                
                diagnosis['recommended_action'] = "RSS يعمل بشكل صحيح"
                
        except Exception as e:
            diagnosis['issues'].append(f"خطأ في تحليل RSS: {str(e)}")
            diagnosis['solutions'].append("تحويل إلى موقع ويب أو البحث عن RSS بديل")
        
        return diagnosis
    
    def _diagnose_website_detailed(self, source, response, diagnosis):
        """تشخيص مفصل لمواقع الويب"""
        print("🌐 تشخيص موقع الويب...")
        
        try:
            content_type = response.headers.get('content-type', '').lower()
            
            if 'html' not in content_type:
                diagnosis['issues'].append(f"ليس HTML: {content_type}")
                diagnosis['solutions'].append("التحقق من صحة الرابط")
                return diagnosis
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # فحص العنوان
            title = soup.find('title')
            if not title or not title.get_text().strip():
                diagnosis['issues'].append("لا يوجد عنوان للصفحة")
            else:
                print(f"   📄 عنوان الصفحة: {title.get_text().strip()[:100]}")
            
            # فحص المحددات الحالية
            current_selectors = {}
            if source.get('selectors'):
                try:
                    current_selectors = json.loads(source['selectors'])
                except:
                    pass
            
            print(f"   🎯 المحددات الحالية: {current_selectors}")
            
            # اختبار المحددات الحالية
            if current_selectors:
                container_selector = current_selectors.get('container', '')
                title_selector = current_selectors.get('title', '')
                
                containers = soup.select(container_selector) if container_selector else []
                print(f"   📦 عناصر الحاوية: {len(containers)}")
                
                if containers:
                    valid_news = 0
                    for container in containers[:5]:
                        title_elem = container.select_one(title_selector) if title_selector else None
                        if title_elem and title_elem.get_text().strip():
                            valid_news += 1
                            print(f"      • {title_elem.get_text().strip()[:80]}")
                    
                    print(f"   ✅ أخبار صحيحة: {valid_news}")
                    
                    if valid_news == 0:
                        diagnosis['issues'].append("المحددات الحالية لا تجد أخبار")
                        diagnosis['solutions'].append("تحديث محددات CSS")
                        # البحث عن محددات جديدة
                        new_selectors = self._find_better_selectors(soup)
                        if new_selectors:
                            diagnosis['new_selectors'] = new_selectors
                            diagnosis['solutions'].append("استخدام محددات CSS محسنة")
                    else:
                        diagnosis['recommended_action'] = "المحددات تعمل - قد تحتاج تحسين"
                else:
                    diagnosis['issues'].append("محدد الحاوية لا يجد عناصر")
                    diagnosis['solutions'].append("تحديث محدد الحاوية")
            else:
                diagnosis['issues'].append("لا توجد محددات CSS")
                diagnosis['solutions'].append("إضافة محددات CSS")
                # البحث عن محددات جديدة
                new_selectors = self._find_better_selectors(soup)
                if new_selectors:
                    diagnosis['new_selectors'] = new_selectors
            
            # فحص وجود محتوى إخباري عام
            news_indicators = soup.select('article, .news, .post, .entry, h1, h2, h3')
            print(f"   📊 مؤشرات إخبارية: {len(news_indicators)}")
            
            if len(news_indicators) < 5:
                diagnosis['issues'].append("محتوى إخباري قليل")
                diagnosis['solutions'].append("التحقق من صحة الرابط أو البحث عن صفحة أخبار")
                
        except Exception as e:
            diagnosis['issues'].append(f"خطأ في تحليل HTML: {str(e)}")
        
        return diagnosis
    
    def _find_better_selectors(self, soup):
        """البحث عن محددات أفضل"""
        
        # قوائم محددات للاختبار
        container_selectors = [
            'article', '.article', '.news-item', '.post', '.entry',
            '.news', '.item', '.story', '.card', '.content-item',
            '.news-box', '.post-item', '.news-card', '.article-item',
            '.story-item', '.news-story', '.content-box', '.item-news',
            '.news-content', '.post-content', '.article-content',
            '.main-news', '.latest-news', '.news-list', '.articles',
            '.posts', '.entries', '.stories', '.content', '.main',
            '[class*="news"]', '[class*="post"]', '[class*="article"]'
        ]
        
        title_selectors = [
            'h1', 'h2', 'h3', 'h4', '.title', '.headline', '.post-title',
            '.news-title', '.article-title', '.story-title', '.entry-title',
            '.item-title', '.content-title', '.news-headline', '.post-headline',
            '.article-headline', 'a[title]', '.link-title', '.news-link',
            '.main-title', '.header-title', '[class*="title"]', '[class*="headline"]'
        ]
        
        best_score = 0
        best_selectors = None
        
        for container in container_selectors:
            containers = soup.select(container)
            if not containers or len(containers) < 2:
                continue
            
            for title_sel in title_selectors:
                score = 0
                valid_items = 0
                
                for cont in containers[:10]:
                    title_elem = cont.select_one(title_sel)
                    if title_elem and title_elem.get_text().strip():
                        valid_items += 1
                        score += len(title_elem.get_text().strip())
                
                if valid_items > best_score:
                    best_score = valid_items
                    best_selectors = {
                        'container': container,
                        'title': title_sel,
                        'content': 'p, .content, .summary, .excerpt, .description',
                        'link': 'a'
                    }
        
        return best_selectors if best_score > 2 else None
    
    def _suggest_alternative_urls(self, original_url):
        """اقتراح روابط بديلة"""
        alternatives = []
        
        try:
            parsed = urlparse(original_url)
            domain = parsed.netloc
            
            # إزالة www
            if domain.startswith('www.'):
                alternatives.append(original_url.replace('www.', ''))
            else:
                alternatives.append(original_url.replace('://', '://www.'))
            
            # تغيير البروتوكول
            if original_url.startswith('https://'):
                alternatives.append(original_url.replace('https://', 'http://'))
            else:
                alternatives.append(original_url.replace('http://', 'https://'))
            
            # اقتراح صفحات فرعية
            if not original_url.endswith('/'):
                alternatives.append(original_url + '/')
            
            alternatives.append(urljoin(original_url, '/news'))
            alternatives.append(urljoin(original_url, '/ar'))
            alternatives.append(urljoin(original_url, '/latest'))
            
        except:
            pass
        
        return alternatives
    
    def run_diagnosis(self):
        """تشغيل التشخيص الشامل"""
        print("🚀 بدء تشخيص المصادر الفاشلة...")
        
        failed_sources = self.get_failed_sources()
        
        if not failed_sources:
            print("✅ لا توجد مصادر فاشلة!")
            return
        
        print(f"📊 تم العثور على {len(failed_sources)} مصدر يحتاج تشخيص")
        
        results = []
        
        for source in failed_sources:
            try:
                diagnosis = self.diagnose_source_detailed(source)
                results.append(diagnosis)
                
                # عرض النتائج
                if diagnosis['issues']:
                    print(f"\n❌ مشاكل في {source['name']}:")
                    for issue in diagnosis['issues']:
                        print(f"   • {issue}")
                
                if diagnosis['solutions']:
                    print(f"💡 الحلول المقترحة:")
                    for solution in diagnosis['solutions']:
                        print(f"   • {solution}")
                
                if diagnosis['alternative_urls']:
                    print(f"🔗 روابط بديلة:")
                    for url in diagnosis['alternative_urls']:
                        print(f"   • {url}")
                
                if diagnosis['new_selectors']:
                    print(f"🎯 محددات CSS محسنة:")
                    print(f"   • الحاوية: {diagnosis['new_selectors']['container']}")
                    print(f"   • العنوان: {diagnosis['new_selectors']['title']}")
                
                time.sleep(2)  # توقف بين المصادر
                
            except Exception as e:
                print(f"❌ خطأ في تشخيص {source['name']}: {str(e)}")
        
        # تقرير نهائي
        print(f"\n📋 تقرير التشخيص النهائي:")
        print(f"📊 إجمالي المصادر المفحوصة: {len(results)}")
        
        fixable = len([r for r in results if r['solutions'] or r['alternative_urls'] or r['new_selectors']])
        print(f"🔧 المصادر القابلة للإصلاح: {fixable}")
        
        return results

if __name__ == '__main__':
    diagnostic = FailedSourcesDiagnostic()
    diagnostic.run_diagnosis()
